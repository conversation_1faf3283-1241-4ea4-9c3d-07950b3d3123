import torch
import torch.nn as nn
import torch.nn.functional as F
from trains.subNets import BertTextEncoder
from trains.subNets.transformers_encoder.transformer import TransformerEncoder
from trains.singleTask.model.router import router
from mamba_modal.mamba_block import MambaBlock
from mamba_modal.CMOE.co_moe import ExpertCollaboration, HierarchicalExpertSystem

class ModalityExpert(nn.Module):
    """低级专家模块，专门处理单一模态的信息"""
    def __init__(self, feature_dim, num_layers):
        super().__init__()
        # 核心处理模块（保留原有的Mamba结构）
        self.processor = MambaBlock(feature_dim, num_layers)
        
        # 添加专家特定组件
        self.attention = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.Tanh(),
            nn.Linear(feature_dim, 1)
        )
        
        # 输出投影层
        self.output_projection = nn.Linear(feature_dim, feature_dim)
        
        # 置信度估计
        self.confidence_estimator = nn.Linear(feature_dim, 1)
    
    def forward(self, x):
        # 通过核心处理模块
        features = self.processor(x)
        
        # 处理Mamba可能返回的元组
        if type(features) == tuple:
            features = features[0]
        
        # 获取最后一个时间步作为特征表示
        features = features[-1]  # [batch_size, feature_dim]
        
        # 计算专家置信度
        confidence = torch.sigmoid(self.confidence_estimator(features))
        
        # 输出投影
        output = self.output_projection(features)
        
        return output, confidence
class DynamicExpertCollaboration(nn.Module):
    def __init__(self, input_dim, feature_dim, num_experts=3):
        super().__init__()
        # 使用与路由网络相似的结构
        self.router = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, num_experts)
        )
        
    def forward(self, input_features, expert_features):
        # 打印输入形状以便调试
        #print(f"Input features shape: {input_features.shape}")
        
        # 确保输入是二维的 [batch_size, features]
        batch_size = expert_features[0].shape[0]
        if len(input_features.shape) > 2:
            # 如果输入是三维的，将其展平为二维
            input_features = input_features.reshape(batch_size, -1)
            #print(f"Reshaped input features: {input_features.shape}")
        
        # 直接从输入特征生成权重，类似路由网络
        logits = self.router(input_features)
        weights = F.softmax(logits, dim=1)
        
        # 加权融合专家输出
        expert_outputs = torch.stack(expert_features, dim=1)  # [batch_size, num_experts, feature_dim]
        weighted_sum = (expert_outputs * weights.unsqueeze(-1)).sum(dim=1)  # [batch_size, feature_dim]
        
        return weighted_sum, weights

class MMOE(nn.Module):
    """
    MMOE 模型的核心实现。
    它整合了三个模态（文本、音频、视频）的输入，并通过一个动态的、基于专家混合（MoE）的机制来融合它们。
    该模型包含两个关键部分：
    1. Mixture of Modality Experts (MoME): 为每个样本动态计算模态权重。
    2. Unimodal Distillation (UD): 利用单模态的预测能力来指导多模态的学习。
    """
    def __init__(self, args):
        """
        模型初始化。
        参数:
            args (Namespace): 包含所有模型配置和超参数的命名空间。
        """
        super(MMOE, self).__init__()
        # 1. 初始化文本编码器 (可选，使用BERT)
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert

        # 2. 从args中解包和设置模型参数
        dst_feature_dims, nheads = args.dst_feature_dim_nheads # 目标特征维度和注意力头数
        
        # 根据数据集名称和是否对齐，设置三个模态的序列长度
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else: # 未对齐的数据有不同的序列长度
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500
        
        self.aligned = args.need_data_aligned # 是否对齐的标志
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims # 原始特征维度
        self.d_l = self.d_a = self.d_v = dst_feature_dims # 变换后统一的特征维度
        self.num_heads = nheads # Transformer中的多头注意力头数
        self.layers = args.nlevels # Transformer的层数
        self.attn_dropout = args.attn_dropout # 通用注意力dropout
        self.attn_dropout_a = args.attn_dropout_a # 音频专用注意力dropout
        self.attn_dropout_v = args.attn_dropout_v # 视频专用注意力dropout
        self.relu_dropout = args.relu_dropout # ReLU激活后的dropout
        self.embed_dropout = args.embed_dropout # 输入嵌入的dropout
        self.res_dropout = args.res_dropout # 残差连接的dropout
        self.output_dropout = args.output_dropout # 输出层的dropout
        self.text_dropout = args.text_dropout # 文本特征的dropout
        self.attn_mask = args.attn_mask # 是否使用注意力掩码
        self.fusion_method = args.fusion_method # 融合方法 ('sum' 或 'concat')
        output_dim = 1 # 回归任务的输出维度为1
        self.args = args

        # 3. 定义模态投影层 (使用1D卷积将不同维度的输入投影到统一维度)
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)

        # 4. 定义编码器层 (在进入Transformer之前对特征进行初步编码)
        # 注意：这里所有模态共享同一个encoder_c，用于提取模态不变特征 (common features)
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        # 这几个编码器在当前实现中并未使用，可能为未来扩展保留
        self.encoder_l = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        self.encoder_v = nn.Conv1d(self.d_v, self.d_v, kernel_size=1, padding=0, bias=False)
        self.encoder_a = nn.Conv1d(self.d_a, self.d_a, kernel_size=1, padding=0, bias=False)

        # 5. 为每个模态实例化独立的Transformer编码器
        """self.self_attentions_l = self.get_network(self_type='l')
        self.self_attentions_v = self.get_network(self_type='v')
        self.self_attentions_a = self.get_network(self_type='a')"""
        
        self.mamba_l = MambaBlock(self.d_l, self.layers)
        self.mamba_a = MambaBlock(self.d_a, self.layers)
        self.mamba_v = MambaBlock(self.d_v, self.layers)
        
        self.expert_l = ModalityExpert(self.d_l, self.layers)
        self.expert_a = ModalityExpert(self.d_a, self.layers)
        self.expert_v = ModalityExpert(self.d_v, self.layers)


        # 6. 为每个单模态定义独立的预测头 (用于Unimodal Distillation)
        self.proj1_l = nn.Linear(self.d_l, self.d_l)
        self.proj2_l = nn.Linear(self.d_l, self.d_l)
        self.out_layer_l = nn.Linear(self.d_l, output_dim)
        self.proj1_v = nn.Linear(self.d_l, self.d_l)
        self.proj2_v = nn.Linear(self.d_l, self.d_l)
        self.out_layer_v = nn.Linear(self.d_l, output_dim)
        self.proj1_a = nn.Linear(self.d_l, self.d_l)
        self.proj2_a = nn.Linear(self.d_l, self.d_l)
        self.out_layer_a = nn.Linear(self.d_l, output_dim)

        # 7. 为融合后的多模态特征定义预测头
        if self.fusion_method == "sum": # 加权求和融合
            self.proj1_c = nn.Linear(self.d_l, self.d_l)
            self.proj2_c = nn.Linear(self.d_l, self.d_l)
            self.out_layer_c = nn.Linear(self.d_l, output_dim)
        elif self.fusion_method == "concat": # 加权拼接融合
            self.proj1_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.proj2_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.out_layer_c = nn.Linear(self.d_l*3, output_dim)

        # 8. 实例化路由网络 (Router Network)，论文MoME部分的核心
        """原始路由网络，修改为专家合作机制"""
        # 输入维度是三个模态特征拼接后的总维度，输出维度是3（对应三个模态的权重）
        #self.Router = router(self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l, 3, self.args.temperature)
        # 使用专家合作机制
        self.expert_collab = DynamicExpertCollaboration(
        # 使用正确的输入维度，与原始路由网络相同
        input_dim=self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l,
        feature_dim=self.d_l, num_experts=3
        )
        
        # 9. 定义线性层，用于在数据未对齐时，将音视频的序列长度变换到与文本相同
        self.transfer_a_ali = nn.Linear(self.len_a, self.len_l)
        self.transfer_v_ali = nn.Linear(self.len_v, self.len_l)


    def forward(self, text, audio, video):
        """
        模型的前向传播逻辑。
        """
        # 1. 文本特征预处理 (如果使用BERT)
        if self.use_bert:
            text = self.text_model(text)
        
        # 将各模态输入转置为 (batch_size, feature_dim, seq_len) 以适应Conv1d
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training)
        x_a = audio.transpose(1, 2)
        x_v = video.transpose(1, 2)

        # 2. 准备送入路由网络(Router)的输入
        # 如果数据未对齐，先通过线性层统一序列长度
        if not self.aligned:
            # permute 用于交换维度以匹配nn.Linear的输入要求
            audio_ = self.transfer_a_ali(audio.permute(0, 2, 1)).permute(0, 2, 1)
            video_ = self.transfer_v_ali(video.permute(0, 2, 1)).permute(0, 2, 1)
            # 拼接三个模态的原始特征
            m_i = torch.cat((text, video_, audio_), dim=2)
        else:
            m_i = torch.cat((text, video, audio), dim=2)
        
        # 打印m_i的形状
        #print(f"m_i shape before reshape: {m_i.shape}")

        # 确保m_i是二维的 [batch_size, features]
        batch_size = text.shape[0]
        m_i_reshaped = m_i.reshape(batch_size, -1)
        #print(f"m_i shape after reshape: {m_i_reshaped.shape}")

        # 4. 特征投影：使用1D卷积将各模态输入投影到统一的特征维度
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)

        # 5. 共享编码器：所有模态通过一个共享的1D卷积层进行初步编码
        c_l = self.encoder_c(proj_x_l)
        c_v = self.encoder_c(proj_x_v)
        c_a = self.encoder_c(proj_x_a)

        # c_l, c_v, c_a 的维度是 [序列长度, 批次大小, 特征维度]
        # 这是为每个模态准备好的、进入各自专属Transformer前的数据
        c_l = c_l.permute(2, 0, 1)
        c_v = c_v.permute(2, 0, 1)
        c_a = c_a.permute(2, 0, 1)

        # 6. 模态专属Transformer编码与池化
        #    以下三个代码块的逻辑完全相同，分别为每个模态执行：
        #    a. 通过各自的Transformer编码器进行深度特征提取。
        #    b. 对Transformer的输出进行池化，将序列信息压缩成一个固定大小的向量。

        # --- 语言模态处理 ---
        c_l_att, c_l_conf = self.expert_l(c_l)
        
        # a. 将语言特征序列送入语言专用的Transformer编码器
        """c_l_att = self.self_attentions_l(c_l)"""
        #print("---------调用mamba_l---------")
        """ c_l_att = self.mamba_l(c_l)
        # b. Transformer的返回值可能是一个元组(特征, 其他信息)，这里只取我们需要的特征部分
        if type(c_l_att) == tuple:
            c_l_att = c_l_att[0]
        # c. 池化操作：取序列的最后一个时间步的输出作为整个序列的代表性特征。
        #    这是一种常见的将序列信息汇总为固定维度向量的方法。
        #    处理后，c_l_att 的维度从 [序列长度, 批次大小, 特征维度] 变为 [批次大小, 特征维度]。
        c_l_att = c_l_att[-1]"""

        # --- 视频模态处理 ---
        c_v_att, c_v_conf = self.expert_v(c_v)
        
        """c_v_att = self.self_attentions_v(c_v)"""
        """c_v_att = self.mamba_v(c_v)
        if type(c_v_att) == tuple:
            c_v_att = c_v_att[0]
        c_v_att = c_v_att[-1]"""
        
        # --- 音频模态处理 ---
        c_a_att, c_a_conf = self.expert_a(c_a)
        
        """c_a_att = self.self_attentions_a(c_a)"""
        """c_a_att = self.mamba_a(c_a)
        if type(c_a_att) == tuple:
            c_a_att = c_a_att[0]
        c_a_att = c_a_att[-1]"""
        #收集专家置信度信息用于后续处理
        modality_confidences = torch.stack([c_l_conf, c_v_conf, c_a_conf], dim=1)  # [batch_size, 3, 1]
        #print(modality_confidences)
        # 7. 计算单模态的预测输出 (logits)，用于 Unimodal Distillation
        l_proj = self.proj2_l(
            F.dropout(F.relu(self.proj1_l(c_l_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        l_proj += c_l_att # 残差连接
        logits_l = self.out_layer_l(l_proj)
        v_proj = self.proj2_v(
            F.dropout(F.relu(self.proj1_v(c_v_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        v_proj += c_v_att
        logits_v = self.out_layer_v(v_proj)
        a_proj = self.proj2_a(
            F.dropout(F.relu(self.proj1_a(c_a_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        a_proj += c_a_att
        logits_a = self.out_layer_a(a_proj)
        
        # 8. 专家合作机制进行特征融合
        # 获取各专家的特征
        expert_features = [c_l_att, c_v_att, c_a_att]
        # 确保所有专家特征的维度一致
        #print(f"Expert features shapes: {[f.shape for f in expert_features]}")
        
        # 使用专家合作机制进行融合
        c_fusion, confidence = self.expert_collab(m_i_reshaped, expert_features)

        # 9. 计算多模态融合后的预测输出
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(c_fusion), inplace=True), p=self.output_dropout,
                      training=self.training))
        c_proj += c_fusion # 残差连接
        logits_c = self.out_layer_c(c_proj)


        # 10. 将所有需要用于计算损失的中间结果打包返回
        res = {
            'logits_c': logits_c,           # 多模态预测结果
            'logits_l': logits_l,           # 文本单模态预测结果
            'logits_v': logits_v,           # 视频单模态预测结果
            'logits_a': logits_a,           # 音频单模态预测结果
            'channel_weight': confidence,    # 专家合作机制的置信度张量
            'c_proj': c_proj,               # 多模态最终特征
            'l_proj': l_proj,               # 文本单模态最终特征
            'v_proj': v_proj,               # 视频单模态最终特征
            'a_proj': a_proj,               # 音频单模态最终特征
            'c_fea': c_fusion,              # 融合后的特征(进入最后proj层之前)
        }
        return res
1.替换原始的transformer层，使用mamba进行低级特征的提取
    用 SIGMA 模块（它融合了双向Mamba和GRU）替换了原有的 Transformer，
    在不破坏模型整体结构的前提下，升级了其序列特征提取的核心能力，使其拥有更好的长时依赖捕捉能力和更高的运行效率。
2.内部状态门控 (Internal State Gating)（属于1中的一部分）
    三个模态的特征（经过初步的Conv1d投影后）被分别送入各自专属的 sigma_processor
    在 sigma_processor_l 内部，GMambaBlock 的内部状态门控对于序列中的每一个时间步（每一个词、每一帧图像）
    执行以下操作：
    Mamba 自带的门控: 动态计算 Δ (Delta)，实现了内容感知的选择性信息处理。
    我们额外添加的门控: 计算 h1 信号，对 Mamba 处理后的输出进行再次的动态调整和缩放。
    内部状态门控工作在模型的核心处理层，专注于单个模态内部，在时间序列的每一个点上进行精细的、内容感知的信息筛选和动态调整。

    实验结果：TEST-(demo) >>  Acc_2: 0.8643  F1_score: 0.8640  Acc_7: 0.4723  MAE: 0.6998  Loss: 0.7002
    Acc_2提升1%，MAE提升1%，F1_score提升1%
===========================================================================================================
当前进度：已完成第一阶段的特征处理，第二阶段特征交互部分，已完成交互，但未充分使用交互后的特征信息...

3.添加模态交互部分，使用fusionmamba实现模态之间的两两交互
    定义一个新的模块 CrossMambaBlock (概念上):（当前仅使用简单的线性层实现交互）
    输入: sequence_A, sequence_B。
    核心操作: 使用cross_selective_scan机制。在为sequence_A计算状态时，其Mamba参数（如Δ, B, C矩阵）会受到sequence_B的动态影响，反之亦然。
    输出: fused_sequence_A, fused_sequence_B。这两个输出序列的每个时间步都包含了来自对方模态的信息。
    在单模态SIGMA处理之后，将特征流一分为二。
    路径一（现有路径）: 立即进行池化，得到c_l_att, c_v_att, c_a_att，专门用于计算单模态损失 (logits_l, logits_v, logits_a)。
    路径二（新增路径）: 将未经池化的完整序列（sigma_output_l, sigma_output_v, sigma_output_a）送入深度交互网络。
    这个网络的输出将仅仅用于最终的多模态融合。

4.对抗蒸馏机制
    核心思想:
    一个好的多模态融合特征c_proj，应该是一个“面目全非”的、无法轻易判断其主要来源的全新特征。
    它不应该“长得太像”任何一个单模态特征。
    引入一个判别器，来强迫融合模块生成这种模态不可知 (Modality-Agnostic) 的特征。
    传统的蒸馏只鼓励相似性，无法保证融合是“创造”而不是“复制”。
    具体实现:
    定义一个判别器 D: 这是一个小型的MLP网络，输入一个特征向量，输出一个3维的概率分布，代表该特征分别属于l, v, a 三个模态的概率。
    训练判别器 :
    判别器的目标是正确分类。将原始的单模态特征l_proj, v_proj, a_proj送入判别器，其训练目标是让D(l_proj)的输出趋近于[1, 0, 0]，D(v_proj)趋近于[0, 1, 0]，以此类推。
    训练融合模块 :
    融合模块（学生）的目标是愚弄判别器。
    在总损失函数中，为融合模块添加一项对抗性损失。这项损失的目标是，让判别器在看到融合特征c_proj时，其输出尽可能地接近均匀分布 [0.33, 0.33, 0.33]。
    对抗损失示例: loss_adv = KL_divergence(D(c_proj), uniform_distribution)
    保证真正的融合: 这种机制从根本上杜绝了融合模块走捷径（比如直接复制最强的单模态特征），强迫它必须对所有模态的信息进行非平凡的、深度的整合，才能生成一个“四不像”的、无法被轻易识别来源的新特征。
    学习模态不变性: 促使模型去学习一种更高层次的、超越具体模态的抽象语义表示。
    范式革新: 将蒸馏从合作学习（Cooperative Learning）范式转换为了对抗学习（Adversarial Learning）范式。


实验结果：
未使用mamba，仅使用线性层实现简单的交互
参数设置:0.3  16,4,2
    TEST-(demo) >>  Acc_2: 0.8537  F1_score: 0.8533  Acc_7: 0.4913  MAE: 0.6878  Loss: 0.6879
参数设置:0.3  8,4,2
    TEST-(demo) >>  Acc_2: 0.8659  F1_score: 0.8654  Acc_7: 0.4665  MAE: 0.6955  Loss: 0.6964
参数设置:0.3  8,4,1 与基线持平
参数设置:0.3  16,4,1 与基线持平
参数设置:0.4  16,4,2
    TEST-(demo) >>  Acc_2: 0.8628  F1_score: 0.8617  Acc_7: 0.4723  MAE: 0.7143  Loss: 0.7138
    TEST-(demo) >>  Acc_2: 0.8582  F1_score: 0.8572  Acc_7: 0.4854  MAE: 0.6895  Loss: 0.6890 
    TEST-(demo) >>  Acc_2: 0.8430  F1_score: 0.8425  Acc_7: 0.5044  MAE: 0.7023  Loss: 0.7019
    TEST-(demo) >>  Acc_2: 0.8598  F1_score: 0.8588  Acc_7: 0.4752  MAE: 0.6934  Loss: 0.6927
    TEST-(demo) >>  Acc_2: 0.8567  F1_score: 0.8558  Acc_7: 0.4840  MAE: 0.6852  Loss: 0.6851
参数设置:0.4  8,4,2 与基线持平
参数设置:0.45  16,4,2 与基线持平
参数设置:0.45  8,4,1 与基线持平
参数设置:0.5  8,4,1 与基线持平

引入mamba
参数设置:0.4  16,4,2
    TEST-(demo) >>  Acc_2: 0.8628  F1_score: 0.8620  Acc_7: 0.4810  MAE: 0.6899  Loss: 0.6894
参数设置:0.4  8,4,2  与基线持平
参数设置:0.4  16,4,1
    











4.备选方案：时序动态路由与融合 (Temporal Dynamic Routing & Fusion)
这个方案旨在打破当前“一个样本一套权重”的静态路由模式。
核心思想:
当前模型的Router对于一个输入样本，只计算出一套全局的模态权重（w的维度是 [batch, 3]），并用这套权重来融合整个序列。但这忽略了信息的重要性是随时间动态变化的。例如，在一个视频片段中，可能前3秒钟的视觉信息（一个微笑）最重要，而后面2秒钟的文本信息（一句讽刺的话）才揭示了真实情感。
本方案提出将Router升级为一个序列到序列的动态权重生成器，在每一个时间步都动态地决定如何融合模-态。
问题根源:
全局静态权重无法捕捉模态重要性的时序变化。
具体实现:
升级Router: 将Router从一个简单的MLP替换为一个轻量级的序列模型（比如一个单层的Mamba或Transformer）。
新的Router输入: Router的输入不再是池化后的全局特征，而是交互后、池化前的完整特征序列拼接，例如 torch.cat([final_l_sequence, final_v_sequence, final_a_sequence], dim=-1)。
新的Router输出: Router的输出将是一个与输入序列等长的权重序列，维度为 [batch, seq_len, 3]。我们称之为w_sequence。
时序动态融合: 在最终融合步骤，不再使用全局权重w，而是在每个时间步上使用对应的权重w_sequence[:, t, :]来进行加权。
    
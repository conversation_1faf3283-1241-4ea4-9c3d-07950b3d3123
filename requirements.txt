beautifulsoup4==4.12.3
certifi==2024.7.4
charset-normalizer==3.3.2
contourpy==1.1.1
cycler==0.12.1
easydict==1.13
filelock==3.15.4
fonttools==4.54.1
fsspec==2024.6.1
gdown==5.2.0
huggingface-hub==0.23.5
idna==3.7
importlib_resources==6.4.5
joblib==1.4.2
kiwisolver==1.4.7
matplotlib==3.7.5
mkl-fft @ file:///croot/mkl_fft_1695058164594/work
mkl-random @ file:///croot/mkl_random_1695059800811/work
mkl-service==2.4.0
numexpr @ file:///croot/numexpr_1683221822650/work
numpy @ file:///work/mkl/numpy_and_numpy_base_1682953417311/work
packaging==24.1
pandas @ file:///croot/pandas_1692289311655/work
pillow==10.4.0
pynvml==11.5.3
pyparsing==3.1.4
PySocks==1.7.1
python-dateutil @ file:///croot/python-dateutil_1716495738603/work
pytz @ file:///croot/pytz_1713974312559/work
PyYAML==6.0.1
regex==2024.5.15
requests==2.32.3
safetensors==0.4.3
scikit-learn==1.3.2
scipy==1.10.1
seaborn==0.13.2
six @ file:///tmp/build/80754af9/six_1644875935023/work
soupsieve==2.6
threadpoolctl==3.5.0
tokenizers==0.19.1
torch==1.9.0+cu111
torchaudio==0.9.0
torchvision==0.10.0+cu111
tqdm==4.66.4
transformers==4.42.4
typing_extensions==4.12.2
tzdata @ file:///croot/python-tzdata_1690578112552/work
urllib3==2.2.2
zipp==3.20.2

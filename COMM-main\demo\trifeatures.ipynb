{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CoMM on synthetic bimodal Trifeatures dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook shows how to use CoMM on the synthetic bimodal-Trifeatures dataset (see Fig. 4 in [our paper](https://arxiv.org/abs/2409.07402)).\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Packages install\n", "\n", "Let's start by installing and loading the required package for this notebook (if not already installed):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install torch\n", "%pip install omegaconf\n", "%pip install hydra-core\n", "%pip install pytorch-lightning\n", "%pip install scikit-learn\n", "%pip install torchvision\n", "%pip install tensorboard\n", "%pip install pandas\n", "%pip install einops\n", "%pip install matplotlib"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "import numpy as np\n", "import os\n", "import torch\n", "import torch.nn.parallel\n", "import torch.optim\n", "import torch.utils.data\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from dataset.trifeatures import TrifeaturesDataModule\n", "from pytorch_lightning import Trainer\n", "from pl_modules.comm import CoMM\n", "from models.mmfusion import MMFusion\n", "from models.input_adapters import PatchedInputAdapter\n", "from models.alexnet import AlexNetEncoder\n", "import warnings"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["torch.manual_seed(42) # for reproducibility\n", "np.random.seed(42) \n", "warnings.filterwarnings(\"ignore\", category=UserWarning) # avoids sklearn warnings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading and visualizing the data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n", "2400 images in train found.\n", "200 images in test found.\n"]}], "source": ["\n", "# Load the unbiased bimodal Trifeatures to learn redundant and unique information (Experiment 1)\n", "data_module_R_U = TrifeaturesDataModule(\"CoMM\", \"bimodal\", batch_size=64, num_workers=16, biased=False)\n", "\n", "# Load the biased bimodal Trifeatures to learn synergistic interactions (Experiment 2)\n", "data_module_S = TrifeaturesDataModule(\"CoMM\", \"bimodal\", batch_size=64, num_workers=16, biased=True)\n", "\n", "# The downstream tasks\n", "downstream_task_S = TrifeaturesDataModule(\"Sup\",\"bimodal\", batch_size=64, num_workers=10, biased=False, task=\"synergy\")\n", "downstream_task_U1 = TrifeaturesDataModule(\"Sup\",\"bimodal\", batch_size=64, num_workers=10, biased=False, task=\"unique1\")\n", "downstream_task_R = TrifeaturesDataModule(\"Sup\",\"bimodal\", batch_size=64, num_workers=10, biased=False, task=\"share\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape is shared, texture is unique to each modality\n"]}, {"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 600x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Fetch 2 pairs of images (i.e. 2 data points)\n", "fig, axes = plt.subplots(2, 2, figsize=(6, 6))\n", "\n", "def norm(x): return (255 * (x - x.min())/(x.max() - x.min())).int()\n", "\n", "for i in range(2):\n", "    pair, _ = downstream_task_S.val_dataset[i]\n", "    img1, img2 = pair  # First and second modality\n", "    img1, img2 = norm(img1).squeeze().permute(1, 2, 0), norm(img2).squeeze().permute(1, 2, 0)  # Convert to HWC format\n", "\n", "    axes[i, 0].imshow(img1)\n", "    axes[i, 0].set_title(f\"Modality 1 (Sample {i})\")\n", "    axes[i, 0].axis(\"off\")\n", "\n", "    axes[i, 1].imshow(img2)\n", "    axes[i, 1].set_title(f\"Modality 2 (Sample {i})\")\n", "    axes[i, 1].axis(\"off\")\n", "print(\"Shape is shared, texture is unique to each modality\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON>t CoMM to Bimodal Trifeatures - Synergy"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["comm = CoMM(\n", "    encoder=MMFusion(\n", "        encoders=[ # Symmetric visual encoders\n", "            AlexNetEncoder(latent_dim=512, global_pool=\"\"), \n", "            AlexNetEncoder(latent_dim=512, global_pool=\"\")\n", "        ], \n", "        input_adapters=[ # Pach adapters for multimodal fusion\n", "            PatchedInputAdapter(num_channels=256, stride_level=1, patch_size_full=1, dim_tokens=512, image_size=6),\n", "            PatchedInputAdapter(num_channels=256, stride_level=1, patch_size_full=1, dim_tokens=512, image_size=6)\n", "        ],\n", "        embed_dim=512\n", "    ),\n", "    projection=CoMM._build_mlp(512, 512, 256),\n", "    optim_kwargs=dict(lr=3e-4, weight_decay=1e-4),\n", "    loss_kwargs=dict(temperature=0.1)\n", ")"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Trainer will use only 1 of 2 GPUs because it is running inside an interactive / notebook environment. You may try to set `Trainer(devices=2)` but please note that multi-GPU inside interactive / notebook environments is considered experimental and unstable. Your mileage may vary.\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n"]}], "source": ["trainer = Trainer(inference_mode=False, max_epochs=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.fit(comm, datamodule=data_module_S)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON>ate CoMM on synergy task"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Extract the embedding and train a linear probe on top:"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["Z_train, y_train = comm.extract_features(downstream_task_S.train_dataloader())\n", "Z_test, y_test = comm.extract_features(downstream_task_S.test_dataloader())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["linear_model = LogisticRegressionCV(Cs=5, n_jobs=10, scoring=\"balanced_accuracy\").fit(Z_train.cpu().detach().numpy(), y_train.cpu().detach().numpy())"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM score on synergy=70.29%\n"]}], "source": ["acc_s = linear_model.score(Z_test.cpu().detach().numpy(), y_test.cpu().detach().numpy())\n", "print(f\"CoMM score on synergy={100 * acc_s:.2f}%\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON>t CoMM on Bimodal Trifeatures - Uniqueness and Redundancy"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["comm = CoMM(\n", "    encoder=MMFusion(\n", "        encoders=[ # Symmetric visual encoders\n", "            AlexNetEncoder(latent_dim=512, global_pool=\"\"), \n", "            AlexNetEncoder(latent_dim=512, global_pool=\"\")\n", "        ], \n", "        input_adapters=[ # Pach adapters for multimodal fusion\n", "            PatchedInputAdapter(num_channels=256, stride_level=1, patch_size_full=1, dim_tokens=512, image_size=6),\n", "            PatchedInputAdapter(num_channels=256, stride_level=1, patch_size_full=1, dim_tokens=512, image_size=6)\n", "        ],\n", "        embed_dim=512\n", "    ),\n", "    projection=CoMM._build_mlp(512, 512, 256),\n", "    optim_kwargs=dict(lr=3e-4, weight_decay=1e-4),\n", "    loss_kwargs=dict(temperature=0.1)\n", ")"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Trainer will use only 1 of 2 GPUs because it is running inside an interactive / notebook environment. You may try to set `Trainer(devices=2)` but please note that multi-GPU inside interactive / notebook environments is considered experimental and unstable. Your mileage may vary.\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n"]}], "source": ["trainer = Trainer(inference_mode=False, max_epochs=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.fit(comm, datamodule=data_module_R_U)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluate CoMM on unique and redundant tasks"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["# Unique task\n", "Z_train, y_train = comm.extract_features(downstream_task_U1.train_dataloader())\n", "Z_test, y_test = comm.extract_features(downstream_task_U1.test_dataloader())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["linear_model = LogisticRegressionCV(Cs=5, n_jobs=10, scoring=\"balanced_accuracy\").fit(Z_train.cpu().detach().numpy(), y_train.cpu().detach().numpy())"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM accuracy on uniqueness=90.14%\n"]}], "source": ["acc_u = linear_model.score(Z_test.cpu().detach().numpy(), y_test.cpu().detach().numpy())\n", "print(f\"CoMM accuracy on uniqueness={100 * acc_u:.2f}%\")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["# Redundant task\n", "Z_train, y_train = comm.extract_features(downstream_task_R.train_dataloader())\n", "Z_test, y_test = comm.extract_features(downstream_task_R.test_dataloader())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["linear_model = LogisticRegressionCV(Cs=5, n_jobs=10, scoring=\"balanced_accuracy\").fit(Z_train.cpu().detach().numpy(), y_train.cpu().detach().numpy())"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM accuracy on redundancy=99.96%\n"]}], "source": ["acc_r = linear_model.score(Z_test.cpu().detach().numpy(), y_test.cpu().detach().numpy())\n", "print(f\"CoMM accuracy on redundancy={100 * acc_r:.2f}%\")"]}], "metadata": {"kernelspec": {"display_name": "multimodal", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}
import torch
import torch.nn as nn
import torch.nn.functional as F
from trains.subNets import BertTextEncoder
from trains.subNets.transformers_encoder.transformer import TransformerEncoder
from trains.singleTask.model.router import router
from trains.utils import MetricsTop, dict_to_str, eva_imp, uni_distill, entropy_balance

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++ 【新增】跨模态注意力模块 (插件) ++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#  该模块旨在通过跨模态注意力捕捉模态间的细粒度交互，为路由网络提供内容感知的先验知识。
#  要启用此功能, 请在配置中设置 args.use_cross_modal_router = True
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class ContentAwareRouter(nn.Module):
    """
    一个轻量级的跨模态注意力模块，用于生成“交互摘要向量”。
    """
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(ContentAwareRouter, self).__init__()
        # 使用一个标准的多头注意力层
        # batch_first=True 表示输入的维度顺序为 (batch, seq_len, feature_dim)
        self.cross_attn = nn.MultiheadAttention(embed_dim=d_model, num_heads=n_heads, dropout=dropout, batch_first=True)
        self.norm = nn.LayerNorm(d_model)

    def forward(self, text_feat, audio_feat, video_feat):
        """
        前向传播。
        参数:
            text_feat, audio_feat, video_feat: 投影后的特征序列，维度为 (batch, feature_dim, seq_len)
        返回:
            interaction_vector: 交互摘要向量，维度为 (batch, feature_dim)
        """
        # 1. 为了适应 MultiheadAttention 的输入格式 (batch, seq, dim), 我们需要转置
        text_feat = text_feat.transpose(1, 2)   # -> (batch, seq_len_l, feature_dim)
        audio_feat = audio_feat.transpose(1, 2) # -> (batch, seq_len_a, feature_dim)
        video_feat = video_feat.transpose(1, 2) # -> (batch, seq_len_v, feature_dim)
        
        # 2. 构建 Query, Key, Value
        #    - Query: 使用文本作为核心查询
        #    - Key/Value: 将音频和视频在序列维度上拼接，作为上下文信息
        query = text_feat
        context = torch.cat([audio_feat, video_feat], dim=1) # -> (batch, seq_len_a + seq_len_v, feature_dim)
        
        # 3. 计算注意力
        #    - self.cross_attn返回 attended_output 和 attention_weights
        #    - 这里我们只关心 attended_output
        attended_output, _ = self.cross_attn(query=query, key=context, value=context)
        attended_output = self.norm(attended_output)
        
        # 4. 池化 (Pooling)
        #    - 对注意力输出的序列进行平均池化，得到一个固定长度的“交互摘要向量”
        interaction_vector = torch.mean(attended_output, dim=1) # -> (batch, feature_dim)
        
        return interaction_vector
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# +++++++++++++++++++++++++++++ 【新增模块结束】 +++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class DEMO(nn.Module):
    """
    EMOE 模型的核心实现。
    它整合了三个模态（文本、音频、视频）的输入，并通过一个动态的、基于专家混合（MoE）的机制来融合它们。
    该模型包含两个关键部分：
    1. Mixture of Modality Experts (MoME): 为每个样本动态计算模态权重。
    2. Unimodal Distillation (UD): 利用单模态的预测能力来指导多模态的学习。
    """
    def __init__(self, args):
        """
        模型初始化。
        参数:
            args (Namespace): 包含所有模型配置和超参数的命名空间。
        """
        super(DEMO, self).__init__()
        # 1. 初始化文本编码器 (可选，使用BERT)
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert

        # 2. 从args中解包和设置模型参数
        dst_feature_dims, nheads = args.dst_feature_dim_nheads # 目标特征维度和注意力头数
        
        # 根据数据集名称和是否对齐，设置三个模态的序列长度
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else: # 未对齐的数据有不同的序列长度
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500
        
        self.aligned = args.need_data_aligned # 是否对齐的标志
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims # 原始特征维度
        self.d_l = self.d_a = self.d_v = dst_feature_dims # 变换后统一的特征维度
        self.num_heads = nheads # Transformer中的多头注意力头数
        self.layers = args.nlevels # Transformer的层数
        self.attn_dropout = args.attn_dropout # 通用注意力dropout
        self.attn_dropout_a = args.attn_dropout_a # 音频专用注意力dropout
        self.attn_dropout_v = args.attn_dropout_v # 视频专用注意力dropout
        self.relu_dropout = args.relu_dropout # ReLU激活后的dropout
        self.embed_dropout = args.embed_dropout # 输入嵌入的dropout
        self.res_dropout = args.res_dropout # 残差连接的dropout
        self.output_dropout = args.output_dropout # 输出层的dropout
        self.text_dropout = args.text_dropout # 文本特征的dropout
        self.attn_mask = args.attn_mask # 是否使用注意力掩码
        self.fusion_method = args.fusion_method # 融合方法 ('sum' 或 'concat')
        output_dim = 1 # 回归任务的输出维度为1
        self.args = args

        # 3. 定义模态投影层 (使用1D卷积将不同维度的输入投影到统一维度)
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)

        # 4. 定义编码器层 (在进入Transformer之前对特征进行初步编码)
        # 注意：这里所有模态共享同一个encoder_c，用于提取模态不变特征 (common features)
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        # 这几个编码器在当前实现中并未使用，可能为未来扩展保留
        self.encoder_l = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        self.encoder_v = nn.Conv1d(self.d_v, self.d_v, kernel_size=1, padding=0, bias=False)
        self.encoder_a = nn.Conv1d(self.d_a, self.d_a, kernel_size=1, padding=0, bias=False)

        # 5. 为每个模态实例化独立的Transformer编码器
        self.self_attentions_l = self.get_network(self_type='l')
        self.self_attentions_v = self.get_network(self_type='v')
        self.self_attentions_a = self.get_network(self_type='a')

        # 6. 为每个单模态定义独立的预测头 (用于Unimodal Distillation)
        self.proj1_l = nn.Linear(self.d_l, self.d_l)
        self.proj2_l = nn.Linear(self.d_l, self.d_l)
        self.out_layer_l = nn.Linear(self.d_l, output_dim)
        self.proj1_v = nn.Linear(self.d_l, self.d_l)
        self.proj2_v = nn.Linear(self.d_l, self.d_l)
        self.out_layer_v = nn.Linear(self.d_l, output_dim)
        self.proj1_a = nn.Linear(self.d_l, self.d_l)
        self.proj2_a = nn.Linear(self.d_l, self.d_l)
        self.out_layer_a = nn.Linear(self.d_l, output_dim)

        # 7. 为融合后的多模态特征定义预测头
        if self.fusion_method == "sum": # 加权求和融合
            self.proj1_c = nn.Linear(self.d_l, self.d_l)
            self.proj2_c = nn.Linear(self.d_l, self.d_l)
            self.out_layer_c = nn.Linear(self.d_l, output_dim)
        elif self.fusion_method == "concat": # 加权拼接融合
            self.proj1_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.proj2_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.out_layer_c = nn.Linear(self.d_l*3, output_dim)

        # 8. 实例化路由网络 (Router Network)，论文MoME部分的核心
        # 输入维度是三个模态特征拼接后的总维度，输出维度是3（对应三个模态的权重）
        # self.Router = router(self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l, 3, self.args.temperature)
        
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++ 【新增】内容感知路由 (插件初始化) ++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        #  通过 args.use_cross_modal_router 控制是否启用。
        #  启用后，会创建一个跨模态注意力模块，并调整原路由网络Router的输入维度。
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        self.use_cross_modal_router = getattr(args, 'use_cross_modal_router', False) # 默认为False
        router_input_dim = self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l

        if self.use_cross_modal_router:
            print(">>> 启用内容感知路由 (Content-Aware Routing is enabled) <<<")
            # 实例化跨模态注意力模块
            self.content_aware_module = ContentAwareRouter(d_model=self.d_l, n_heads=self.num_heads)
            # 增强后的路由输入维度 = 原始展平特征维度 + 交互摘要向量维度
            router_input_dim += self.d_l
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        
        self.Router = router(router_input_dim, 3, self.args.temperature)

        # 9. 定义线性层，用于在数据未对齐时，将音视频的序列长度变换到与文本相同
        self.transfer_a_ali = nn.Linear(self.len_a, self.len_l)
        self.transfer_v_ali = nn.Linear(self.len_v, self.len_l)


    def get_network(self, self_type='l', layers=-1):
        """
        一个工厂/辅助方法，用于根据指定的模态类型来创建和配置一个完整的Transformer编码器网络。
        这种设计模式使得为不同模态（文本、音频、视频）创建具有细微差别（如不同的dropout率）
        的专属编码器变得非常方便，同时又最大化地复用了代码。

        参数:
            self_type (str): 指定要创建的编码器所属的模态类型。
                             可选值为 'l' (语言), 'a' (音频), 'v' (视频)。
            layers (int): 可选参数，用于覆盖在args中设置的默认Transformer层数。
                          如果为-1，则使用默认层数。

        返回:
            TransformerEncoder: 一个配置完成的、准备好处理序列数据的Transformer编码器实例。
        """
        # 1. 根据模态类型，选择对应的特征维度和注意力dropout率。
        #    这允许我们为不同模态（例如，音频可能需要比文本更高的dropout率）进行精细化的超参数调整。
        if self_type == 'l':
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        elif self_type == 'a':
            embed_dim, attn_dropout = self.d_a, self.attn_dropout_a
        elif self_type == 'v':
            embed_dim, attn_dropout = self.d_v, self.attn_dropout_v
        else:
            raise ValueError("未知的网络类型")

        # 2. 实例化并返回一个TransformerEncoder。
        #    这里传入了所有从args中读取的、与Transformer相关的超参数。
        return TransformerEncoder(embed_dim=embed_dim,             # 特征维度，例如 128
                                  num_heads=self.num_heads,         # 多头注意力机制中的"头"数，例如 8
                                  layers=max(self.layers, layers),  # 编码器层数，例如 4
                                  attn_dropout=attn_dropout,        # 注意力权重矩阵的dropout率
                                  relu_dropout=self.relu_dropout,   # 全连接层中ReLU激活后的dropout率
                                  res_dropout=self.res_dropout,     # 残差连接上的dropout率
                                  embed_dropout=self.embed_dropout,# 输入嵌入（embedding）的dropout率
                                  attn_mask=self.attn_mask)         # 是否在自注意力计算中使用掩码

    def get_net(self, name):
        """一个简单的辅助函数，通过名字获取模型的一个层。"""
        return getattr(self, name)

    def forward(self, text, audio, video):
        """
        模型的前向传播逻辑。
        """
        # 1. 文本特征预处理 (如果使用BERT)
        if self.use_bert:
            text = self.text_model(text)
        
        # 将各模态输入转置为 (batch_size, feature_dim, seq_len) 以适应Conv1d
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training)
        x_a = audio.transpose(1, 2)
        x_v = video.transpose(1, 2)

        # --- 【代码逻辑调整】为了让内容感知模块能够使用投影后的特征，我们将投影步骤提前 ---
        # 4. 特征投影：使用1D卷积将各模态输入投影到统一的特征维度 (原位置在路由输入计算之后)
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)

        # 2. 准备送入路由网络(Router)的输入
        # 如果数据未对齐，先通过线性层统一序列长度
        if not self.aligned:
            # permute 用于交换维度以匹配nn.Linear的输入要求
            audio_ = self.transfer_a_ali(audio.permute(0, 2, 1)).permute(0, 2, 1)
            video_ = self.transfer_v_ali(video.permute(0, 2, 1)).permute(0, 2, 1)
            # 拼接三个模态的原始特征
            m_i = torch.cat((text, video_, audio_), dim=2)
        else:
            m_i = torch.cat((text, video, audio), dim=2)
        
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++ 【新增】内容感知路由 (插件逻辑) ++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        #  如果启用了内容感知路由，则在此处计算交互摘要向量，并将其拼接到路由器的输入中。
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        router_input = m_i.reshape(m_i.shape[0], -1) # 原始的展平输入
        if self.use_cross_modal_router:
            # 1. 计算交互摘要向量
            #    输入是投影后的特征: proj_x_l, proj_x_a, proj_x_v
            interaction_vector = self.content_aware_module(proj_x_l, proj_x_a, proj_x_v)
            
            # 2. 将交互摘要向量与原始展平输入拼接
            router_input = torch.cat([router_input, interaction_vector], dim=1)
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

        # 3. 调用路由网络，计算模态权重 m_w
        m_w = self.Router(router_input)

        # 4. 特征投影：使用1D卷积将各模态输入投影到统一的特征维度
        # proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)
        # proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)
        # proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)

        # 5. 共享编码器：所有模态通过一个共享的1D卷积层进行初步编码
        c_l = self.encoder_c(proj_x_l)
        c_v = self.encoder_c(proj_x_v)
        c_a = self.encoder_c(proj_x_a)

        # c_l, c_v, c_a 的维度是 [序列长度, 批次大小, 特征维度]
        # 这是为每个模态准备好的、进入各自专属Transformer前的数据
        c_l = c_l.permute(2, 0, 1)
        c_v = c_v.permute(2, 0, 1)
        c_a = c_a.permute(2, 0, 1)

        # 6. 模态专属Transformer编码与池化
        #    以下三个代码块的逻辑完全相同，分别为每个模态执行：
        #    a. 通过各自的Transformer编码器进行深度特征提取。
        #    b. 对Transformer的输出进行池化，将序列信息压缩成一个固定大小的向量。

        # --- 语言模态处理 ---
        # a. 将语言特征序列送入语言专用的Transformer编码器
        c_l_att = self.self_attentions_l(c_l)
        # b. Transformer的返回值可能是一个元组(特征, 其他信息)，这里只取我们需要的特征部分
        if type(c_l_att) == tuple:
            c_l_att = c_l_att[0]
        # c. 池化操作：取序列的最后一个时间步的输出作为整个序列的代表性特征。
        #    这是一种常见的将序列信息汇总为固定维度向量的方法。
        #    处理后，c_l_att 的维度从 [序列长度, 批次大小, 特征维度] 变为 [批次大小, 特征维度]。
        c_l_att = c_l_att[-1]

        # --- 视频模态处理 ---
        c_v_att = self.self_attentions_v(c_v)
        if type(c_v_att) == tuple:
            c_v_att = c_v_att[0]
        c_v_att = c_v_att[-1]
        
        # --- 音频模态处理 ---
        c_a_att = self.self_attentions_a(c_a)
        if type(c_a_att) == tuple:
            c_a_att = c_a_att[0]
        c_a_att = c_a_att[-1]

        # 7. 计算单模态的预测输出 (logits)，用于 Unimodal Distillation
        l_proj = self.proj2_l(
            F.dropout(F.relu(self.proj1_l(c_l_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        l_proj += c_l_att # 残差连接
        logits_l = self.out_layer_l(l_proj)
        v_proj = self.proj2_v(
            F.dropout(F.relu(self.proj1_v(c_v_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        v_proj += c_v_att
        logits_v = self.out_layer_v(v_proj)
        a_proj = self.proj2_a(
            F.dropout(F.relu(self.proj1_a(c_a_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        a_proj += c_a_att
        logits_a = self.out_layer_a(a_proj)

        """添加交互部分"""
        #print("c_l_att.shape, c_v_att.shape, c_a_att.shape", c_l_att.shape, c_v_att.shape, c_a_att.shape)
        
        # 8. 动态特征融合 (Dynamic Fusion)
        if self.fusion_method == "sum": 
            # 遍历batch中的每个样本，使用其专属的权重 m_w[i] 进行加权求和
            for i in range(m_w.shape[0]):
                c_f = c_l_att[i] * m_w[i][0] + c_v_att[i] * m_w[i][1] + c_a_att[i] * m_w[i][2]
                if i == 0: c_fusion = c_f.unsqueeze(0)
                else: c_fusion = torch.cat([c_fusion, c_f.unsqueeze(0)], dim=0)   
        elif self.fusion_method == "concat":        
            # 遍历batch中的每个样本，进行加权拼接
            for i in range(m_w.shape[0]):
                c_f = torch.cat([c_l_att[i] * m_w[i][0], c_v_att[i] * m_w[i][1], c_a_att[i] * m_w[i][2]], dim=0) * 3
                if i == 0: c_fusion = c_f.unsqueeze(0)
                else: c_fusion = torch.cat([c_fusion, c_f.unsqueeze(0)], dim=0)   


        # 9. 计算多模态融合后的预测输出
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(c_fusion), inplace=True), p=self.output_dropout,
                      training=self.training))
        c_proj += c_fusion # 残差连接
        logits_c = self.out_layer_c(c_proj)


        # 10. 将所有需要用于计算损失的中间结果打包返回
        res = {
            'logits_c': logits_c,           # 多模态预测结果
            'logits_l': logits_l,           # 文本单模态预测结果
            'logits_v': logits_v,           # 视频单模态预测结果
            'logits_a': logits_a,           # 音频单模态预测结果
            'channel_weight': m_w,          # 路由网络输出的模态权重
            'c_proj': c_proj,               # 多模态最终特征
            'l_proj': l_proj,               # 文本单模态最终特征
            'v_proj': v_proj,               # 视频单模态最终特征
            'a_proj': a_proj,               # 音频单模态最终特征
            'c_fea': c_fusion,              # 融合后的特征(进入最后proj层之前)
        }
        return res
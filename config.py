import json
import os
from pathlib import Path
from easydict import EasyDict as edict
import argparse


def get_config_regression(model_name, dataset_name, config_file=""):
    """
    Get the regression config of given dataset and model from config file.

    Parameters:
        config_file (str): Path to config file, if given an empty string, will use default config file.
        model_name (str): Name of model.
        dataset_name (str): Name of dataset.

    Returns:
        config (dict): config of the given dataset and model
    """
    if config_file == "":
        config_file = Path(__file__).parent / "config" / "config_regression.json"
    with open(config_file, 'r') as f:
        config_all = json.load(f)
    model_common_args = config_all[model_name]['commonParams']
    model_dataset_args = config_all[model_name]['datasetParams'][dataset_name]
    dataset_args = config_all['datasetCommonParams'][dataset_name]
    # use aligned feature if the model requires it, otherwise use unaligned feature
    dataset_args = dataset_args['aligned'] if (model_common_args['need_data_aligned'] and 'aligned' in dataset_args) else dataset_args['unaligned']

    config = {}
    config['model_name'] = model_name
    config['dataset_name'] = dataset_name
    config.update(dataset_args)
    config.update(model_common_args)
    config.update(model_dataset_args)
    config['featurePath'] = os.path.join(config_all['datasetCommonParams']['dataset_root_dir'], config['featurePath'])
    config = edict(config)

    return config


# 添加双向蒸馏相关配置
def add_bidirectional_distill_args(parser):
    """
    添加双向蒸馏相关的配置参数
    """
    parser.add_argument('--use_bidirectional_distill', type=bool, default=True,
                        help='是否使用双向蒸馏')
    parser.add_argument('--forward_distill_weight', type=float, default=0.1,
                        help='前向蒸馏权重（单模态→融合）')
    parser.add_argument('--backward_distill_weight', type=float, default=0.05,
                        help='反向蒸馏权重（融合→单模态）')
    parser.add_argument('--distill_weight', type=float, default=0.1,
                        help='总蒸馏损失权重')
    parser.add_argument('--distill_method', type=str, default='mse', choices=['mse', 'kl'],
                        help='蒸馏损失计算方法')
    parser.add_argument('--distill_temperature', type=float, default=1.0,
                        help='蒸馏温度参数（用于KL散度）')
    parser.add_argument('--weighted_backward_distill', type=bool, default=False,
                        help='是否使用模态权重加权反向蒸馏损失')
    parser.add_argument('--log_distill', type=bool, default=False,
                        help='是否记录蒸馏损失详情')
    return parser


# 以下函数在实际项目中可能已经存在，这里仅为示例
def add_model_args(parser):
    """模型相关参数"""
    parser.add_argument('--model_name', type=str, default='mmoe', help='模型名称')
    parser.add_argument('--layers', type=int, default=5, help='Transformer层数')
    parser.add_argument('--hidden_size', type=int, default=128, help='隐藏层大小')
    return parser

def add_data_args(parser):
    """数据相关参数"""
    parser.add_argument('--dataset', type=str, default='mosi', help='数据集名称')
    parser.add_argument('--aligned', type=bool, default=True, help='是否对齐数据')
    return parser

def add_optimization_args(parser):
    """优化相关参数"""
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.1, help='权重衰减')
    return parser


# 在主函数中调用这个方法
def get_config(parser=None):
    if parser is None:
        parser = argparse.ArgumentParser()
        
    # 现有参数添加
    parser = add_model_args(parser)
    parser = add_data_args(parser)
    parser = add_optimization_args(parser)
    
    # 添加双向蒸馏相关参数
    parser = add_bidirectional_distill_args(parser)
    
    return parser



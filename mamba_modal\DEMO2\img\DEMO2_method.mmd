flowchart TB
  %% 输入
  subgraph Inputs[输入]
    T[文本 Text]
    A[音频 Audio]
    V[视频 Video]
  end

  T -->|可选| BERT[BERT 编码]
  BERT --> PL[1D Conv 投影 proj_l]
  T -.无需BERT时直连.-> PL
  A --> PA[1D Conv 投影 proj_a]
  V --> PV[1D Conv 投影 proj_v]

  PL --> ENC[共享卷积 encoder_c]
  PA --> ENC
  PV --> ENC

  ENC --> CL[c_l^0: 语言序列 -> 末步池化]
  ENC --> CA[c_a^0: 音频序列 -> 末步池化]
  ENC --> CV[c_v^0: 视觉序列 -> 末步池化]

  %% 序列建模（每模态一套 SIGMA）
  subgraph SIGMA[每模态 SIGMA (GMambaBlock + FFN)]
    direction LR
    CL --> CLs[c_l]
    CA --> CAs[c_a]
    CV --> CVs[c_v]
  end

  %% ICE-MoE 迭代
  subgraph ICE[ICE-MoE 迭代 t = 1..K]
    direction LR
    CLs --> UM1[单模态头 z_l^t]
    CAs --> UM2[单模态头 z_a^t]
    CVs --> UM3[单模态头 z_v^t]

    RIN[路由输入: t=1 用 m_i; t>1 用 concat(z_·^t)] --> ROUTER[Router softmax/τ]
    ROUTER --> W[w^t 模态权重]

    UM1 --> RIN
    UM2 --> RIN
    UM3 --> RIN

    W --> FUSE[加权融合 (sum/concat) + 残差]
    FUSE --> MMH[多模态头 z_multi^t]

    MMH --> CAL[KL 校准 (z_multi^t || Σ w^t z_·^t)]
    FUSE --> FB[反馈: 投影到各模态并更新]
  end

  AfterK[跨轮加权 Σ_t softmax(α)_t · F^t] --> MoE[MoE 专家+门控 + 残差]
  MoE --> OUT[最终回归 logits_c]
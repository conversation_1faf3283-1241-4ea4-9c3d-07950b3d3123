# 导入必要的库
import gc          # 垃圾回收模块，用于内存管理
import logging     # 日志记录模块
import os          # 操作系统接口模块
import time        # 时间处理模块
from pathlib import Path  # 路径处理模块
import numpy as np        # 数值计算库
import pandas as pd       # 数据处理库
import torch              # PyTorch深度学习框架

# 导入项目自定义模块
from config import get_config_regression    # 获取回归任务配置
from data_loader import MMDataLoader        # 多模态数据加载器
from trains import ATIO                     # 训练器工厂类
from utils import assign_gpu, setup_seed    # GPU分配和随机种子设置工具
from trains.singleTask.model import emoe    # EMOE模型
import sys

# 设置CUDA环境变量
os.environ["CUDA_DEVICE_ORDER"]="PCI_BUS_ID"        # 按PCI总线ID排序CUDA设备
os.environ["CUBLAS_WORKSPACE_CONFIG"] = ":4096:2"   # 配置CUBLAS工作空间以确保可重现性

# 创建日志记录器
logger = logging.getLogger('EMOE')
# 设置默认使用第0号GPU
torch.cuda.set_device(0)

def _set_logger(log_dir, model_name, dataset_name, verbose_level):
    """
    设置日志记录器

    Args:
        log_dir: 日志文件保存目录
        model_name: 模型名称
        dataset_name: 数据集名称
        verbose_level: 详细程度级别 (0: ERROR, 1: INFO, 2: DEBUG)

    Returns:
        配置好的日志记录器对象
    """
    # 构建日志文件路径：log_dir/model_name-dataset_name.log
    log_file_path = Path(log_dir) / f"{model_name}-{dataset_name}.log"
    # 获取EMOE日志记录器
    logger = logging.getLogger('EMOE')
    # 设置日志记录器的最低级别为DEBUG
    logger.setLevel(logging.DEBUG)

    # 创建文件处理器，将日志写入文件
    fh = logging.FileHandler(log_file_path)
    # 设置文件日志的格式：时间戳 - 记录器名称 [级别] - 消息
    fh_formatter = logging.Formatter('%(asctime)s - %(name)s [%(levelname)s] - %(message)s')
    fh.setLevel(logging.DEBUG)  # 文件中记录所有DEBUG及以上级别的日志
    fh.setFormatter(fh_formatter)
    logger.addHandler(fh)

    # 创建控制台处理器，将日志输出到终端
    stream_level = {0: logging.ERROR, 1: logging.INFO, 2: logging.DEBUG}
    ch = logging.StreamHandler()
    ch.setLevel(stream_level[verbose_level])  # 根据verbose_level设置控制台输出级别
    # 设置控制台日志的格式：记录器名称 - 消息
    ch_formatter = logging.Formatter('%(name)s - %(message)s')
    ch.setFormatter(ch_formatter)
    logger.addHandler(ch)

    return logger


def EMOE_run(
    model_name, dataset_name, config=None, config_file="", seeds=[], is_tune=False,
    tune_times=500, feature_T="", feature_A="", feature_V="",
    model_save_dir="", res_save_dir="", log_dir="",
    gpu_ids=[0], num_workers=3, verbose_level=1, mode = ''
):
    """
    EMOE模型的主运行函数

    Args:
        model_name: 模型名称
        dataset_name: 数据集名称
        config: 额外的配置字典，用于覆盖默认配置
        config_file: 配置文件路径，默认使用config/config.json
        seeds: 随机种子列表，用于多次实验
        is_tune: 是否进行超参数调优
        tune_times: 调优次数
        feature_T: 文本特征类型
        feature_A: 音频特征类型
        feature_V: 视觉特征类型
        model_save_dir: 模型保存目录
        res_save_dir: 结果保存目录
        log_dir: 日志保存目录
        gpu_ids: 使用的GPU ID列表
        num_workers: 数据加载的工作进程数
        verbose_level: 日志详细程度
        mode: 运行模式（训练或测试）
    """
    # 将模型名和数据集名转换为小写，保持一致性
    model_name = model_name.lower()
    dataset_name = dataset_name.lower()

    # 配置文件路径处理
    if config_file != "":
        config_file = Path(config_file)
    else:
        # 默认使用项目根目录下的config/config.json
        config_file = Path(__file__).parent / "config" / "config.json"
    if not config_file.is_file():
        raise ValueError(f"Config file {str(config_file)} not found.")

    # 设置默认目录路径并创建目录
    if model_save_dir == "":
        model_save_dir = Path.home() / "EMOE" / "saved_models"  # 模型保存目录
    Path(model_save_dir).mkdir(parents=True, exist_ok=True)

    if res_save_dir == "":
        res_save_dir = Path.home() / "EMOE" / "results"  # 结果保存目录
    Path(res_save_dir).mkdir(parents=True, exist_ok=True)

    if log_dir == "":
        log_dir = Path.home() / "EMOE" / "logs"  # 日志保存目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)

    # 设置默认随机种子列表，用于多次实验确保结果的可靠性
    seeds = seeds if seeds != [] else [1111, 1112, 1113, 1114, 1115]
    # 设置日志记录器
    logger = _set_logger(log_dir, model_name, dataset_name, verbose_level)
    


    # 获取回归任务的配置参数
    args = get_config_regression(model_name, dataset_name, config_file)
    args.mode = mode  # 设置运行模式（训练或测试）
    # 设置模型保存路径
    args['model_save_path'] = Path(model_save_dir) / f"{args['model_name']}-{args['dataset_name']}.pth"
    # 分配GPU设备
    args['device'] = assign_gpu(gpu_ids)
    # 设置训练模式为回归任务
    args['train_mode'] = 'regression'
    # 设置各模态的特征类型
    args['feature_T'] = feature_T  # 文本特征类型
    args['feature_A'] = feature_A  # 音频特征类型
    args['feature_V'] = feature_V  # 视觉特征类型
    # 如果提供了额外配置，更新参数
    if config:
        args.update(config)

    # 创建正常实验结果保存目录
    res_save_dir = Path(res_save_dir) / "normal"
    res_save_dir.mkdir(parents=True, exist_ok=True)

    # 存储多次实验的结果
    model_results = []
    # 使用不同随机种子进行多次实验
    for i, seed in enumerate(seeds):
        setup_seed(seed)  # 设置随机种子确保实验可重现
        args['cur_seed'] = i + 1  # 记录当前是第几次实验
        # 运行单次实验
        result = _run(args, num_workers, is_tune)
        model_results.append(result)
    # 如果启用了蒸馏模式，保存实验结果到CSV文件
    if args.is_distill:
        # 获取评估指标名称列表
        criterions = list(model_results[0].keys())
        # 构建CSV文件路径
        csv_file = res_save_dir / f"{dataset_name}.csv"

        # 读取或创建CSV文件
        if csv_file.is_file():
            df = pd.read_csv(csv_file)  # 如果文件存在，读取现有数据
        else:
            # 如果文件不存在，创建新的DataFrame，列名为模型名和各评估指标
            df = pd.DataFrame(columns=["Model"] + criterions)

        # 计算并保存结果统计信息
        res = [model_name]  # 结果行以模型名开始
        for c in criterions:
            # 提取所有实验中该指标的值
            values = [r[c] for r in model_results]
            # 计算均值和标准差（乘以100转换为百分比形式）
            mean = round(np.mean(values)*100, 2)
            std = round(np.std(values)*100, 2)
            # 将均值和标准差作为元组添加到结果中
            res.append((mean, std))

        # 将结果添加到DataFrame并保存
        df.loc[len(df)] = res
        df.to_csv(csv_file, index=None)
        logger.info(f"Results saved to {csv_file}.")

def _run(args, num_workers=4, is_tune=False, from_sena=False):
    """
    执行单次EMOE模型训练或测试

    Args:
        args: 配置参数对象
        num_workers: 数据加载的工作进程数
        is_tune: 是否进行超参数调优
        from_sena: 是否来自SENA（未使用的参数）

    Returns:
        测试结果字典
    """
    # 创建多模态数据加载器
    dataloader = MMDataLoader(args, num_workers)

    print("training for EMOE")

    # 设置低层次特征的参数
    args.gd_size_low = 64          # 低层次特征的梯度下降批次大小
    args.w_losses_low = [1, 10]    # 低层次特征的损失权重
    args.metric_low = 'l1'         # 低层次特征的评估指标

    # 设置高层次特征的参数
    args.gd_size_high = 32         # 高层次特征的梯度下降批次大小
    args.w_losses_high = [1, 10]   # 高层次特征的损失权重
    args.metric_high = 'l1'        # 高层次特征的评估指标

    # 设置模态索引：0-文本，1-视觉，2-音频
    from_idx = [0, 1, 2]
    assert len(from_idx) >= 1  # 确保至少使用一个模态

    # 创建EMOE模型并移动到GPU
    model = getattr(emoe, 'EMOE')(args).cuda()

    # 获取训练器对象
    trainer = ATIO().getTrain(args)

    # 根据运行模式执行不同的操作
    if args.mode == 'test':
        # 测试模式：加载预训练模型并进行测试
        model.load_state_dict(torch.load('pt/mosi-aligned.pth'))  # 加载预训练的模型权重
        results = trainer.do_test(model, dataloader['test'], mode="TEST")  # 在测试集上评估
        sys.stdout.flush()  # 刷新标准输出缓冲区
        input('[Press Any Key to start another run]')  # 等待用户输入继续
    else:
        # 训练模式：训练模型然后测试
        # 执行训练过程
        epoch_results = trainer.do_train(model, dataloader, return_epoch_results=from_sena)
        # 加载训练过程中保存的最佳模型
        model.load_state_dict(torch.load('pt/emoe.pth'))

        # 在测试集上评估最佳模型的性能
        results = trainer.do_test(model, dataloader['test'], mode="TEST", f=1)

        # 清理内存资源
        del model                    # 删除模型对象
        torch.cuda.empty_cache()     # 清空CUDA缓存
        gc.collect()                 # 强制垃圾回收
        time.sleep(1)                # 短暂休眠确保资源释放完成

    return results
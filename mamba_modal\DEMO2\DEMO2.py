# 导入必要的库
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from tqdm import tqdm
# 导入自定义工具函数：评估指标、字典转字符串、评估重要性、统一蒸馏、熵平衡
from trains.utils import MetricsTop, dict_to_str, eva_imp, uni_distill, entropy_balance

# 创建日志记录器
logger = logging.getLogger('DEMO')

class DEMO():
    """
    EMOE (Expert Mixture of Experts) 类
    用于多模态情感分析的专家混合模型训练和测试

    该类实现了基于专家混合机制的多模态融合方法，
    能够自适应地学习不同模态（文本、音频、视觉）的重要性权重
    """
    def __init__(self, args):
        """
        初始化EMOE类

        Args:
            args: 包含训练参数的配置对象
        """
        self.args = args
        # 使用L1损失函数作为主要的回归损失
        self.criterion = nn.L1Loss()
        # 根据训练模式和数据集名称获取相应的评估指标
        self.metrics = MetricsTop(args.train_mode).getMetics(args.dataset_name)

    def do_train(self, model, dataloader, return_epoch_results=False):
        """
        执行模型训练过程

        Args:
            model: 要训练的EMOE模型
            dataloader: 包含训练、验证、测试数据的数据加载器字典
            return_epoch_results: 是否返回每个epoch的详细结果

        Returns:
            如果return_epoch_results为True，返回包含每个epoch结果的字典；否则返回None
        """
        # 获取模型的所有参数
        params = list(model.parameters())
        # 使用Adam优化器
        optimizer = optim.Adam(params, lr=self.args.learning_rate)
        # 使用ReduceLROnPlateau学习率调度器，当验证损失不再下降时减少学习率
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, verbose=True, patience=self.args.patience)

        # 初始化epoch计数器和最佳epoch记录
        epochs, best_epoch = 0, 0
        # 如果需要返回每个epoch的结果，初始化结果存储字典
        if return_epoch_results:
            epoch_results = {
                'train': [],    # 训练结果
                'valid': [],    # 验证结果
                'test': []      # 测试结果
            }
        # 根据关键评估指标确定是最小化还是最大化目标
        min_or_max = 'min' if self.args.KeyEval in ['Loss'] else 'max'
        # 初始化最佳验证结果
        best_valid = 1e8 if min_or_max == 'min' else 0

        # 开始训练循环
        while True:
            epochs += 1
            # 初始化预测值和真实值列表
            y_pred, y_true = [], []
            # 设置模型为训练模式
            model.train()
            train_loss = 0.0

            # 梯度累积机制：每update_epochs个batch更新一次参数
            left_epochs = self.args.update_epochs
            # 使用tqdm显示训练进度
            with tqdm(dataloader['train']) as td:
                for batch_data in td:
                    # 如果是新的累积周期开始，清零梯度
                    if left_epochs == self.args.update_epochs:
                        optimizer.zero_grad()
                    left_epochs -= 1

                    # 将数据移动到指定设备（GPU/CPU）
                    vision = batch_data['vision'].to(self.args.device)  # 视觉特征
                    audio = batch_data['audio'].to(self.args.device)    # 音频特征
                    text = batch_data['text'].to(self.args.device)      # 文本特征
                    labels = batch_data['labels']['M'].to(self.args.device)  # 多模态标签
                    labels = labels.view(-1, 1)  # 调整标签形状

                    # 前向传播：输入三种模态数据到模型
                    output = model(text, audio, vision)
                    # 获取通道权重（专家混合权重）
                    w = output['channel_weight']
                    #print(w)  # 调试用，打印权重

                    # 收集预测值和真实值用于后续评估
                    y_pred.append(output['logits_c'].cpu())  # 融合后的预测结果
                    y_true.append(labels.cpu())

                    # 计算各个模态的任务损失
                    loss_task_l = self.criterion(output['logits_l'], labels)  # 文本模态损失
                    loss_task_v = self.criterion(output['logits_v'], labels)  # 视觉模态损失
                    loss_task_a = self.criterion(output['logits_a'], labels)  # 音频模态损失
                    loss_task_m = self.criterion(output['logits_c'], labels)  # 多模态融合损失

                    # 计算各模态的重要性距离（预测误差）
                    l_dist = eva_imp(output['logits_l'], labels)  # 文本模态的预测误差
                    a_dist = eva_imp(output['logits_a'], labels)  # 音频模态的预测误差
                    v_dist = eva_imp(output['logits_v'], labels)  # 视觉模态的预测误差

                    # 根据预测误差计算理想的模态权重分布
                    dist = torch.zeros(l_dist.shape[0], 3).to(self.args.device)
                    for i,_ in enumerate(l_dist):
                        # 计算权重：误差越小，权重越大（使用倒数关系）
                        s = 1/(l_dist[i]+0.1) + 1/(v_dist[i]+0.1) + 1/(a_dist[i]+0.1)  # 归一化因子
                        dist[i][0] = (1/(l_dist[i]+0.1)) / s  # 文本权重
                        dist[i][1] = (1/(v_dist[i]+0.1)) / s  # 视觉权重
                        dist[i][2] = (1/(a_dist[i]+0.1)) / s  # 音频权重

                    # 计算权重相似性损失：让学习到的权重接近理想权重分布
                    loss_sim = torch.mean(torch.mean((dist.detach() - w) ** 2, dim=-1))
                    # 计算熵平衡损失：防止权重过于集中在某个模态
                    loss_ety = entropy_balance(w)

                    # 计算统一蒸馏损失：让融合特征学习加权后的单模态特征
                    if self.args.fusion_method == "sum":
                        # 加权求和融合方式
                        loss_ud = uni_distill(output['c_proj'], (output['l_proj'] * w[:,0].view(-1, 1) + output['v_proj'] * w[:,1].view(-1, 1) +
                        output['a_proj'] * w[:,2].view(-1, 1)).detach())
                    elif self.args.fusion_method == "concat":
                        # 加权拼接融合方式
                        loss_ud = uni_distill(output['c_proj'], torch.cat([output['l_proj'] * w[:,0].view(-1, 1),output['v_proj'] * w[:,1].view(-1, 1),
                        output['a_proj'] * w[:,2].view(-1, 1)], dim=1).detach())

                    # 首先提取校准损失
                    calibration_losses = output['iteration_results']['calibration_losses']
                    total_calibration_loss = sum(calibration_losses) / len(calibration_losses)  # 平均校准损失
                    loss_load_balance = output['loss_load_balance']
                    # 总损失计算：
                    # 1. 主任务损失 (loss_task_m)
                    # 2. 单模态任务损失的平均值 ((loss_task_l + loss_task_v + loss_task_a)/3)
                    # 3. 正则化项：熵平衡损失和权重相似性损失 (0.1*(loss_ety + 0.1*loss_sim))
                    # 4. 统一蒸馏损失 (0.1*loss_ud)
                    loss = loss_task_m + (loss_task_l + loss_task_v + loss_task_a)/3 
                    + 0.1*(loss_ety + 0.1*loss_sim) + 0.1*loss_ud + 0.1*total_calibration_loss
                    + 0.02 * loss_load_balance

                    # 反向传播计算梯度
                    loss.backward()
                    train_loss += loss.item()

                    # 梯度累积：当累积的batch数达到设定值时，执行参数更新
                    if not left_epochs:
                        optimizer.step()
                        left_epochs = self.args.update_epochs
                # 处理最后一个不完整的累积周期
                if not left_epochs:
                    optimizer.step()

            # 计算平均训练损失
            train_loss = train_loss / len(dataloader['train'])
            # 合并所有batch的预测结果和真实标签
            pred, true = torch.cat(y_pred), torch.cat(y_true)
            # 计算训练集上的评估指标
            train_results = self.metrics(pred, true)
            # 记录训练日志
            logger.info(
                f">> Epoch: {epochs} "
                f"TRAIN-({self.args.model_name}) [{epochs - best_epoch}/{epochs}/{self.args.cur_seed}] "
                f">> total_loss: {round(train_loss, 4)} "
                f"{dict_to_str(train_results)}"
            )
            # 在验证集和测试集上评估模型性能
            val_results = self.do_test(model, dataloader['valid'], mode="VAL")
            test_results = self.do_test(model, dataloader['test'], mode="TEST")
            # 获取当前验证集上的关键评估指标值
            cur_valid = val_results[self.args.KeyEval]
            # 根据验证损失调整学习率
            scheduler.step(val_results['Loss'])
            # 保存当前epoch的模型状态
            torch.save(model.state_dict(), './pt/' + str(epochs) + '.pth')

            # 判断当前模型是否比之前的最佳模型更好
            isBetter = cur_valid <= (best_valid - 1e-6) if min_or_max == 'min' else cur_valid >= (best_valid + 1e-6)
            if isBetter:
                # 更新最佳验证结果和对应的epoch
                best_valid, best_epoch = cur_valid, epochs
                # 保存最佳模型
                model_save_path = './pt/emoe.pth'
                torch.save(model.state_dict(), model_save_path)

            # 如果需要返回每个epoch的详细结果，保存当前epoch的结果
            if return_epoch_results:
                train_results["Loss"] = train_loss
                epoch_results['train'].append(train_results)
                epoch_results['valid'].append(val_results)
                test_results = self.do_test(model, dataloader['test'], mode="TEST")
                epoch_results['test'].append(test_results)

            # 早停机制：如果验证性能在指定epoch数内没有改善，停止训练
            if epochs - best_epoch >= self.args.early_stop:
                return epoch_results if return_epoch_results else None

    def do_test(self, model, dataloader, mode="VAL", return_sample_results=False, f=0):
        """
        执行模型测试/验证过程

        Args:
            model: 要测试的EMOE模型
            dataloader: 测试数据的数据加载器
            mode: 测试模式，可以是"VAL"（验证）或"TEST"（测试）
            return_sample_results: 是否返回样本级别的详细结果
            f: 未使用的参数（保留用于兼容性）

        Returns:
            包含评估结果的字典
        """
        # 设置模型为评估模式
        model.eval()
        # 初始化预测值和真实值列表
        y_pred, y_true = [], []
        # 初始化权重和能力列表（当前未使用）
        weight, ability = [], []
        c_fea = []

        eval_loss = 0.0
        # 如果需要返回样本级别的结果，初始化相关变量
        if return_sample_results:
            ids, sample_results = [], []
            all_labels = []
            features = {
                "Feature_t": [],  # 文本特征
                "Feature_a": [],  # 音频特征
                "Feature_v": [],  # 视觉特征
                "Feature_f": [],  # 融合特征
            }

        # 禁用梯度计算以节省内存和加速推理
        with torch.no_grad():
            # 使用tqdm显示测试进度
            with tqdm(dataloader) as td:
                for batch_data in td:
                    # 将数据移动到指定设备
                    vision = batch_data['vision'].to(self.args.device)  # 视觉特征
                    audio = batch_data['audio'].to(self.args.device)    # 音频特征
                    text = batch_data['text'].to(self.args.device)      # 文本特征
                    labels = batch_data['labels']['M'].to(self.args.device)  # 多模态标签
                    labels = labels.view(-1, 1)  # 调整标签形状

                    # 前向传播获取模型输出
                    output = model(text, audio, vision)

                    # 计算损失
                    loss = self.criterion(output['logits_c'], labels)
                    eval_loss += loss.item()
                    # 收集预测结果和真实标签
                    y_pred.append(output['logits_c'].cpu())
                    y_true.append(labels.cpu())
                    
        # 计算平均评估损失
        eval_loss = eval_loss / len(dataloader)
        # 合并所有batch的预测结果和真实标签
        pred, true = torch.cat(y_pred), torch.cat(y_true)
        # 计算评估指标
        eval_results = self.metrics(pred, true)
        # 添加损失到结果字典
        eval_results["Loss"] = round(eval_loss, 4)
        # 记录评估日志
        logger.info(f"{mode}-({self.args.model_name}) >> {dict_to_str(eval_results)}")

        # 如果需要返回样本级别的详细结果
        if return_sample_results:
            eval_results["Ids"] = ids  # 样本ID
            eval_results["SResults"] = sample_results  # 样本级别结果
            # 合并所有特征
            for k in features.keys():
                features[k] = np.concatenate(features[k], axis=0)
            eval_results['Features'] = features  # 特征表示
            eval_results['Labels'] = all_labels  # 所有标签

        return eval_results
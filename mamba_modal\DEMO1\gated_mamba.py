import torch.nn.functional as F
import torch
from torch import nn
from functools import partial
# 导入 Mamba-ssm 模型，这是 SIGMA 模型的核心依赖之一
from mamba_ssm import Mamba
# 导入 RecBole 的基础模块，用于构建序列化推荐模型
from recbole.model.abstract_recommender import SequentialRecommender
from recbole.model.loss import BPRLoss
from recbole.model.layers import TransformerEncoder, FeatureSeqEmbLayer, VanillaAttention
import math
import numpy as np

# 定义 SIGMA 模型的主体，继承自 RecBole 的 SequentialRecommender
class SIGMA(SequentialRecommender):
    def __init__(self, config, dataset):
        super(SIGMA, self).__init__(config, dataset)

        # 从配置文件中加载模型参数
        self.hidden_size = config["hidden_size"]  # 隐藏层维度
        self.loss_type = config["loss_type"]      # 损失函数类型 (BPR 或 CE)
        self.num_layers = config["num_layers"]    # SIGMALayers 的层数
        self.dropout_prob = config["dropout_prob"]# Dropout 概率
        
        # Mamba 模块的超参数
        self.d_state = config["d_state"]  # Mamba 的状态维度 (N)
        self.d_conv = config["d_conv"]    # Mamba 的卷积核大小 (D)
        self.expand = config["expand"]    # Mamba 的扩展因子 (E)

        # 物品嵌入层，padding_idx=0 表示索引为0的物品是填充符，不参与计算
        self.item_embedding = nn.Embedding(
            self.n_items, self.hidden_size, padding_idx=0
        )
  
        # 定义层归一化和 Dropout
        self.LayerNorm = nn.LayerNorm(self.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(self.dropout_prob)
        
        # 创建一个 ModuleList，其中包含多层 SIGMALayers
        # 这是模型的核心部分，堆叠多个 SIGMA 层来处理序列信息
        self.mamba_layers = nn.ModuleList([
            SIGMALayers(
                d_model=self.hidden_size,
                d_state=self.d_state,
                d_conv=self.d_conv,
                expand=self.expand,
                dropout=self.dropout_prob,
                num_layers=self.num_layers,
            ) for _ in range(self.num_layers)
        ])
        
        # 根据配置选择损失函数
        if self.loss_type == "BPR":
            self.loss_fct = BPRLoss()
        elif self.loss_type == "CE":
            self.loss_fct = nn.CrossEntropyLoss()
        else:
            raise NotImplementedError("请确保 'loss_type' 是 'BPR' 或 'CE'!")

        # 初始化模型权重
        self.apply(self._init_weights)

    # 模型权重初始化函数
    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Embedding)):
            # 对线性层和嵌入层的权重进行正态分布初始化
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            # 对层归一化的偏置置零，权重置一
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            # 对线性层的偏置置零
            module.bias.data.zero_()

    # 模型的前向传播逻辑
    def forward(self, item_seq, item_seq_len):
        # 1. 获取物品序列的嵌入向量
        item_emb = self.item_embedding(item_seq)
        # 2. 应用 Dropout 和 LayerNorm
        item_emb = self.dropout(item_emb)
        item_emb = self.LayerNorm(item_emb)

        # 3. 将嵌入向量依次传入每一层 SIGMALayer
        for i in range(self.num_layers):
            item_emb = self.mamba_layers[i](item_emb)
        
        # 4. 提取每个序列最后一个有效物品的隐藏状态，作为用户的最终兴趣表示
        seq_output = self.gather_indexes(item_emb, item_seq_len - 1)
        
        return seq_output

    # 计算损失函数
    def calculate_loss(self, interaction):
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]
        seq_output = self.forward(item_seq, item_seq_len)
        pos_items = interaction[self.POS_ITEM_ID]
        
        # BPR 损失：适用于正负样本对的场景
        if self.loss_type == "BPR":
            neg_items = interaction[self.NEG_ITEM_ID]
            pos_items_emb = self.item_embedding(pos_items)
            neg_items_emb = self.item_embedding(neg_items)
            pos_score = torch.sum(seq_output * pos_items_emb, dim=-1)  # [B]
            neg_score = torch.sum(seq_output * neg_items_emb, dim=-1)  # [B]
            loss = self.loss_fct(pos_score, neg_score)
            return loss
        # CE 损失：适用于全物品排序的场景
        else:  # self.loss_type = 'CE'
            test_item_emb = self.item_embedding.weight
            logits = torch.matmul(seq_output, test_item_emb.transpose(0, 1))
            loss = self.loss_fct(logits, pos_items)
            return loss

    # 在预测阶段，计算用户对特定测试物品的评分
    def predict(self, interaction):
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]
        test_item = interaction[self.ITEM_ID]
        seq_output = self.forward(item_seq, item_seq_len)
        test_item_emb = self.item_embedding(test_item)
        scores = torch.mul(seq_output, test_item_emb).sum(dim=1)  # [B]
        return scores

    # 在全排序评估中，计算用户对所有物品的评分
    def full_sort_predict(self, interaction):
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]
        seq_output = self.forward(item_seq, item_seq_len)
        test_items_emb = self.item_embedding.weight
        scores = torch.matmul(
            seq_output, test_items_emb.transpose(0, 1)
        )  # [B, n_items]
        return scores


# 定义 G-Mamba (Gated Mamba) 块，这是 SIGMA 的核心创新点
class GMambaBlock(nn.Module):
    def __init__(self, d_model, d_state, d_conv, expand):
        super(GMambaBlock, self).__init__()
        # 可学习的权重，用于融合三个分支的输出：GRU, 反向Mamba, 正向Mamba
        self.combining_weights = nn.Parameter(torch.tensor([0.1, 0.1, 0.8], dtype=torch.float32))
        
        # 论文中提到的 DS Gate (Dense Selective Gate) 的线性变换层
        self.dense1 = nn.Linear(d_model, d_model)
        self.dense2 = nn.Linear(d_model, d_model)
        # 最后的投影层
        self.projection = nn.Linear(d_model, d_model)

        # Mamba 模型实例，用于处理序列信息
        self.mamba = Mamba(
            d_model=d_model,
            d_state=d_state,
            d_conv=d_conv,
            expand=expand,
        )
        # GRU 模型实例 (论文中的 FE-GRU)，用于捕捉短期依赖
        self.gru = nn.GRU(d_model, d_model, num_layers=1, bias=False,batch_first=True)
        
        # 论文中的 DS Gate (Dense Selective Gate)，分为 Sigmoid 和 SiLU 两个门
        self.selective_gate_sig = nn.Sequential(
            nn.Sigmoid(),
            nn.Linear(d_model, d_model)
          
        )
        self.selective_gate_si = nn.Sequential(
            nn.SiLU(),
            nn.Linear(d_model, d_model)
            
        )
        # 门控后的 Dropout 层
        self.selective_gate = nn.Sequential(
            nn.Dropout(0.2),
        )
        # 一维卷积层，用于为 GRU 提取特征
        self.conv1d = nn.Conv1d(d_model, d_model, kernel_size=3, padding=1)

    # 权重初始化（当前代码未调用）
    def initialize_weights(self):
            with torch.no_grad():
                self.combining_weights.data = F.softmax(self.combining_weights.data, dim=0)
                self.weights.data = F.softmax(self.weights.data, dim=0)

    # G-Mamba 块的前向传播
    def forward(self, input_tensor):
        self.gru.flatten_parameters()
        # 对融合权重进行 Softmax，确保其和为1
        combining_weights = F.softmax(self.combining_weights.data, dim=0)
        
        # 论文中 DS Gate 的一部分，用于生成门控信号 h1 和 h2
        h1 = self.dense1(input_tensor)
        
        # FE-GRU 分支：使用 Conv1d 对输入进行特征提取，然后送入 GRU
        g1 = self.conv1d(input_tensor.transpose(1, 2))
        g1 = g1.transpose(1, 2)
    
        # PF-Mamba (Partially Flipped Mamba) 的实现
        # 复制输入张量，用于创建反向序列
        flipped_input = input_tensor.clone()
        # 对序列的大部分（除了最后几个）进行翻转，以构建一个部分反向的序列
        # 注意：这里的 :45 是一个硬编码，可能需要根据实际序列长度进行调整
        flipped_input[:, :45, :] = input_tensor[:, :45, :].flip(dims=[1])
        h2 = flipped_input
        h2 = self.dense2(h2)
        h2 = self.dense2(flipped_input) + flipped_input
        
        # Mamba 的双向处理
        mamba_output_f = self.mamba(flipped_input)  # 反向 Mamba
        mamba_output = self.mamba(input_tensor)    # 正向 Mamba
        
        # 应用 DS Gate
        h1 = input_tensor + h1
        # 结合 SiLU 和 Sigmoid 门控
        h1 = self.selective_gate_si(h1) + self.selective_gate_sig(h1)
        h1 = self.selective_gate(h1)

        h2 = self.selective_gate_si(h2) + self.selective_gate_sig(h1)
        h2 = self.selective_gate(h2)
            
        # 将门控信号应用到 Mamba 的输出上
        mamba_output = mamba_output * h1 + mamba_output
        mamba_output_f = mamba_output_f * h2 +  mamba_output_f 
        
        # 获取 GRU 的输出
        gru_output, _ = self.gru(g1)
    
        # 加权融合三个分支的输出
        combined_states = (
          self.combining_weights[2] * mamba_output +      # 正向 Mamba
          self.combining_weights[1] * mamba_output_f+   # 反向 Mamba
          self.combining_weights[0] * gru_output         # GRU
        )
        # 最终通过一个线性投影层
        combined_states = self.projection(combined_states)
        return combined_states

# SIGMA 层，结构类似于 Transformer 的 Encoder Layer
class SIGMALayers(nn.Module):
    def __init__(self, d_model, d_state, d_conv, expand, dropout, num_layers):
        super().__init__()
        self.num_layers = num_layers
        # 核心的 G-Mamba 块
        self.gmamba = GMambaBlock(d_model=d_model, d_state=d_state, d_conv=d_conv, expand=expand)
        self.dropout = nn.Dropout(dropout)
        self.LayerNorm = nn.LayerNorm(d_model, eps=1e-12)
        # 前馈神经网络 (FFN)
        self.ffn = FeedForward(d_model=d_model, inner_size=d_model*4, dropout=dropout)

    def forward(self, input_tensor):
            # 1. 通过 G-Mamba 块
            hidden_states = self.gmamba(input_tensor)
            
            # 2. 应用残差连接、Dropout 和 LayerNorm
            if self.num_layers == 1:        # 如果只有一层，则不使用残差连接
                hidden_states = self.LayerNorm(self.dropout(hidden_states))
            else:                           # 如果是多层堆叠，则使用残差连接
                hidden_states = self.LayerNorm(self.dropout(hidden_states) + input_tensor)
            
            # 3. 通过前馈神经网络
            hidden_states = self.ffn(hidden_states)
            return hidden_states

# 标准的前馈神经网络 (Feed-Forward Network)
class FeedForward(nn.Module):
    def __init__(self, d_model, inner_size, dropout=0.2):
        super().__init__()
        self.w_1 = nn.Linear(d_model, inner_size) # 第一个线性层，将维度从 d_model 扩展到 inner_size
        self.w_2 = nn.Linear(inner_size, d_model) # 第二个线性层，将维度从 inner_size 缩减回 d_model
        self.activation = nn.GELU() # GELU 激活函数
        self.dropout = nn.Dropout(dropout)
        self.LayerNorm = nn.LayerNorm(d_model, eps=1e-12)

    def forward(self, input_tensor):
        # FFN 的计算流程: Linear -> GELU -> Dropout -> Linear -> Dropout
        hidden_states = self.w_1(input_tensor)
        hidden_states = self.activation(hidden_states)
        hidden_states = self.dropout(hidden_states)

        hidden_states = self.w_2(hidden_states)
        hidden_states = self.dropout(hidden_states)
        
        # 应用残差连接和层归一化
        hidden_states = self.LayerNorm(hidden_states + input_tensor)

        return hidden_states



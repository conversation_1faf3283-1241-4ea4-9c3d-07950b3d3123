{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CoMM on MIMIC dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook will show how to use CoMM on the [MIMIC dataset](https://github.com/pliang279/MultiBench) (see Table 1 in [our paper](https://arxiv.org/abs/2409.07402)). "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Packages install and loading\n", "\n", "We start by installing and loading the required packages for this notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install torch\n", "%pip install omegaconf\n", "%pip install hydra-core\n", "%pip install pytorch-lightning\n", "%pip install scikit-learn\n", "%pip install torchvision\n", "%pip install tensorboard\n", "%pip install pandas\n", "%pip install einops\n", "%pip install matplotlib\n", "%pip install gdown"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "import numpy as np\n", "import torch\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from dataset.multibench import MultiBenchDataModule\n", "from pytorch_lightning import Trainer\n", "from pl_modules.comm import CoMM\n", "from models.mmfusion import MMFusion\n", "from models.input_adapters import FeaturesInputAdapter\n", "from models.mlp import MLP\n", "from models.gru import GRU\n", "import warnings"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["torch.manual_seed(45) # for reproducibility\n", "np.random.seed(45) \n", "warnings.filterwarnings(\"ignore\", category=UserWarning) # avoids sklearn warnings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the data "]}, {"cell_type": "markdown", "metadata": {}, "source": ["MultiBench consists in 15 preprocessed datasets with predefined train/val/test splits. In this notebook, we will focus on MIMIC, a dataset that requires credientials before download (check https://mimic.mit.edu).\n", "\n", "Don't forget to set the correct data path in **dataset/catalog.json** ! "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load MIMIC \n", "data_module_mimic = MultiBenchDataModule(\"mimic\", model=\"CoMM\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"tabular\", \"timeseries\"], \n", "                                        augmentations=\"drop+noise\")\n", "\n", "downstream_mimic = MultiBenchDataModule(\"mimic\", model=\"Sup\",\n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"tabular\", \"timeseries\"],\n", "                                        task=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate CoMM on MIMIC data"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def classification_scoring(model, data_module, scoring=\"balanced_accuracy\"):\n", "    Z_train, y_train = model.extract_features(data_module.train_dataloader())\n", "    Z_test, y_test = model.extract_features(data_module.test_dataloader())\n", "    linear_model = LogisticRegressionCV(Cs=5, n_jobs=10, scoring=scoring)\n", "    linear_model.fit(Z_train.cpu().detach().numpy(), y_train.cpu().detach().numpy())\n", "    return linear_model.score(Z_test.cpu().detach().numpy(), y_test.cpu().detach().numpy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MIMIC "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["comm = CoMM(\n", "    encoder=MMFusion(\n", "        encoders=[ # Handles tabular and timeseries data  \n", "            MLP(indim=5, hiddim=10, outdim=10, dropout=False), \n", "            GRU(indim=12, hiddim=512, dropout=False, batch_first=True), \n", "        ], \n", "        input_adapters=[FeaturesInputAdapter(n_features=10, dim_tokens=512), None], # No adapters needed\n", "        embed_dim=512\n", "    ),\n", "    projection=CoMM._build_mlp(512, 512, 256),\n", "    optim_kwargs=dict(lr=1e-3, weight_decay=1e-2),\n", "    loss_kwargs=dict(temperature=0.1)\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Trainer will use only 1 of 2 GPUs because it is running inside an interactive / notebook environment. You may try to set `Trainer(devices=2)` but please note that multi-GPU inside interactive / notebook environments is considered experimental and unstable. Your mileage may vary.\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n"]}], "source": ["trainer = Trainer(inference_mode=False, max_epochs=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.fit(comm, datamodule=data_module_mimic)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = classification_scoring(comm, downstream_mimic)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM accuracy on MIMIC=66.68\n"]}], "source": ["print(f\"CoMM accuracy on MIMIC={100 * score:.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "multimodal", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}
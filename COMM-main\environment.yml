name: multimodal
channels:
  - pytorch
  - anaconda
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - abseil-cpp=20230802.0=hd4dd3e8_1
  - absl-py=1.4.0=py38h06a4308_0
  - aiohttp=3.8.5=py38h5eee18b_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - antlr-python-runtime=4.9.3=pyhd8ed1ab_1
  - anyio=3.5.0=py38h06a4308_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py38h7f8727e_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - async-lru=2.0.4=py38h06a4308_0
  - async-timeout=4.0.2=py38h06a4308_0
  - attrs=23.1.0=py38h06a4308_0
  - babel=2.11.0=py38h06a4308_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - beautifulsoup4=4.12.2=py38h06a4308_0
  - blas=1.0=mkl
  - bleach=4.1.0=pyhd3eb1b0_0
  - blinker=1.6.2=py38h06a4308_0
  - bottleneck=1.3.5=py38h7deecbd_0
  - brotli=1.0.9=h9c3ff4c_4
  - brotlipy=0.7.0=py38h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.19.1=h5eee18b_0
  - ca-certificates=2023.08.22=h06a4308_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - certifi=2023.11.17=py38h06a4308_0
  - cffi=1.15.1=py38h5eee18b_3
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.1.7=py38h06a4308_0
  - comm=0.1.2=py38h06a4308_0
  - contourpy=1.0.5=py38hdb19cb5_0
  - cryptography=41.0.3=py38hdda0065_0
  - cuda-cudart=11.8.89=0
  - cuda-cupti=11.8.87=0
  - cuda-libraries=11.8.0=0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvtx=11.8.86=0
  - cuda-runtime=11.8.0=0
  - cycler=0.12.1=pyhd8ed1ab_0
  - cyrus-sasl=2.1.28=h52b45da_1
  - dbus=1.13.18=hb2f20db_0
  - debugpy=1.6.7=py38h6a678d5_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - einops=0.7.0=pyhd8ed1ab_1
  - executing=0.8.3=pyhd3eb1b0_0
  - expat=2.5.0=h6a678d5_0
  - fastcache=1.1.0=py38h7b6447c_0
  - ffmpeg=4.2.2=h20bf706_0
  - filelock=3.9.0=py38h06a4308_0
  - fontconfig=2.14.1=h4c34cd2_2
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - frozenlist=1.3.3=py38h5eee18b_0
  - fsspec=2023.9.2=py38h06a4308_0
  - ftfy=6.1.1=pyhd8ed1ab_0
  - giflib=5.2.1=h5eee18b_3
  - glib=2.69.1=he621ea3_2
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py38heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - google-auth=2.22.0=py38h06a4308_0
  - google-auth-oauthlib=0.5.2=py38h06a4308_0
  - grpc-cpp=1.48.2=he1ff14a_3
  - grpcio=1.48.2=py38he1ff14a_3
  - gst-plugins-base=1.14.1=h6a678d5_1
  - gstreamer=1.14.1=h5eee18b_1
  - h5py=3.9.0=py38he06866b_0
  - hdf5=1.12.1=h2b7332f_3
  - huggingface_hub=0.17.3=py38h06a4308_0
  - hydra-core=1.3.2=pyhd8ed1ab_0
  - icu=58.2=hf484d3e_1000
  - idna=3.4=py38h06a4308_0
  - importlib-metadata=6.0.0=py38h06a4308_0
  - importlib_metadata=6.0.0=hd3eb1b0_0
  - importlib_resources=6.1.0=pyhd8ed1ab_0
  - intel-openmp=2023.1.0=hdb19cb5_46305
  - ipykernel=6.25.0=py38h2f386ee_0
  - ipython=8.12.2=py38h06a4308_0
  - ipywidgets=8.0.4=py38h06a4308_0
  - jedi=0.18.1=py38h06a4308_1
  - jinja2=3.1.2=py38h06a4308_0
  - joblib=1.2.0=py38h06a4308_0
  - jpeg=9e=h5eee18b_1
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=4.19.2=py38h06a4308_0
  - jsonschema-specifications=2023.7.1=py38h06a4308_0
  - jupyter=1.0.0=py38h06a4308_8
  - jupyter-lsp=2.2.0=py38h06a4308_0
  - jupyter_client=8.6.0=py38h06a4308_0
  - jupyter_console=6.6.3=py38h06a4308_0
  - jupyter_core=5.5.0=py38h06a4308_0
  - jupyter_events=0.8.0=py38h06a4308_0
  - jupyter_server=2.10.0=py38h06a4308_0
  - jupyter_server_terminals=0.4.4=py38h06a4308_1
  - jupyterlab=4.0.8=py38h06a4308_0
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.25.1=py38h06a4308_0
  - jupyterlab_widgets=3.0.9=py38h06a4308_0
  - kiwisolver=1.4.4=py38h6a678d5_0
  - krb5=1.20.1=h143b758_1
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libclang=14.0.6=default_hc6dbbc7_1
  - libclang13=14.0.6=default_he11475f_1
  - libcublas=11.11.3.6=0
  - libcufft=10.9.0.58=0
  - libcufile=1.7.2.10=0
  - libcups=2.4.2=h2d74bed_1
  - libcurand=10.3.3.141=0
  - libcurl=8.4.0=h251f7ec_0
  - libcusolver=11.4.1.48=0
  - libcusparse=11.7.5.86=0
  - libdeflate=1.17=h5eee18b_1
  - libedit=3.1.20221030=h5eee18b_0
  - libev=4.33=h7f8727e_1
  - libevent=2.1.12=hdbd6064_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=11.2.0=h00389a5_1
  - libgfortran5=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libllvm14=14.0.6=hdb19cb5_3
  - libnghttp2=1.57.0=h2d74bed_0
  - libnpp=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libopus=1.3.1=h7b6447c_0
  - libpng=1.6.39=h5eee18b_0
  - libpq=12.15=hdbd6064_1
  - libprotobuf=3.20.3=he621ea3_0
  - libsodium=1.0.18=h7b6447c_0
  - libssh2=1.10.0=hdbd6064_2
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libvpx=1.7.0=h439df22_0
  - libwebp=1.3.2=h11a3e52_0
  - libwebp-base=1.3.2=h5eee18b_0
  - libxcb=1.15=h7f8727e_0
  - libxkbcommon=1.0.1=h5eee18b_1
  - libxml2=2.10.4=hcbfbd50_0
  - libxslt=1.1.37=h2085143_0
  - lightning-utilities=0.9.0=py38h06a4308_0
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_0
  - markdown=3.4.1=py38h06a4308_0
  - markupsafe=2.1.1=py38h7f8727e_0
  - matplotlib=3.7.2=py38h06a4308_0
  - matplotlib-base=3.7.2=py38h1128e8f_0
  - matplotlib-inline=0.1.6=py38h06a4308_0
  - mistune=2.0.4=py38h06a4308_0
  - mkl=2023.1.0=h213fc3f_46343
  - mkl-service=2.4.0=py38h5eee18b_1
  - mkl_fft=1.3.8=py38h5eee18b_0
  - mkl_random=1.2.4=py38hdb19cb5_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.3.0=py38h06a4308_0
  - multidict=6.0.2=py38h5eee18b_0
  - munkres=1.1.4=pyh9f0ad1d_0
  - mysql=5.7.24=h721c034_2
  - nbclient=0.8.0=py38h06a4308_0
  - nbconvert=7.10.0=py38h06a4308_0
  - nbformat=5.9.2=py38h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.5.6=py38h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.1=py38h06a4308_0
  - nltk=3.8.1=py38h06a4308_0
  - notebook=7.0.6=py38h06a4308_0
  - notebook-shim=0.2.3=py38h06a4308_0
  - numexpr=2.8.4=py38hc78ab66_1
  - numpy=1.24.3=py38hf6e8229_1
  - numpy-base=1.24.3=py38h060ed82_1
  - oauthlib=3.2.2=py38h06a4308_0
  - omegaconf=2.3.0=pyhd8ed1ab_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.4.0=h3ad879b_0
  - openssl=3.0.12=h7f8727e_0
  - overrides=7.4.0=py38h06a4308_0
  - packaging=23.1=py38h06a4308_0
  - pandas=2.0.3=py38h1128e8f_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pcre=8.45=h9c3ff4c_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=10.0.1=py38ha6cbd5a_0
  - pip=23.3=py38h06a4308_0
  - pkgutil-resolve-name=1.3.10=py38h06a4308_0
  - platformdirs=3.10.0=py38h06a4308_0
  - ply=3.11=py_1
  - pooch=1.7.0=py38h06a4308_0
  - prometheus_client=0.14.1=py38h06a4308_0
  - prompt-toolkit=3.0.36=py38h06a4308_0
  - prompt_toolkit=3.0.36=hd3eb1b0_0
  - protobuf=3.20.3=py38h6a678d5_0
  - psutil=5.9.0=py38h5eee18b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pygments=2.15.1=py38h06a4308_1
  - pyjwt=2.4.0=py38h06a4308_0
  - pyopenssl=23.2.0=py38h06a4308_0
  - pyparsing=3.0.9=pyhd8ed1ab_0
  - pyqt=5.15.7=py38h6a678d5_1
  - pyqt5-sip=12.11.0=py38h6a678d5_1
  - pysocks=1.7.1=py38h06a4308_0
  - python=3.8.18=h955ad1f_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py38h06a4308_0
  - python-json-logger=2.0.7=py38h06a4308_0
  - python-tzdata=2023.3=pyhd3eb1b0_0
  - pytorch=2.1.0=py3.8_cuda11.8_cudnn8.7.0_0
  - pytorch-cuda=11.8=h7e8668a_5
  - pytorch-lightning=2.1.1=pyhd8ed1ab_0
  - pytorch-mutex=1.0=cuda
  - pytz=2023.3.post1=py38h06a4308_0
  - pyyaml=6.0.1=py38h5eee18b_0
  - pyzmq=25.1.0=py38h6a678d5_0
  - qt-main=5.15.2=h7358343_9
  - qt-webengine=5.15.9=hbbf29b9_6
  - qtconsole=5.5.0=py38h06a4308_0
  - qtpy=2.4.1=py38h06a4308_0
  - qtwebkit=5.212=h3fafdc1_5
  - re2=2022.04.01=h295c915_0
  - readline=8.2=h5eee18b_0
  - referencing=0.30.2=py38h06a4308_0
  - regex=2023.10.3=py38h5eee18b_0
  - requests=2.31.0=py38h06a4308_0
  - requests-oauthlib=1.3.0=py_0
  - rfc3339-validator=0.1.4=py38h06a4308_0
  - rfc3986-validator=0.1.1=py38h06a4308_0
  - rpds-py=0.10.6=py38hb02cf49_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - safetensors=0.4.0=py38ha89cbab_0
  - scikit-learn=1.3.0=py38h1128e8f_0
  - scipy=1.10.1=py38hf6e8229_1
  - seaborn=0.12.2=py38h06a4308_0
  - send2trash=1.8.2=py38h06a4308_0
  - sentence-transformers=2.2.2=pyhd8ed1ab_0
  - sentencepiece=0.1.99=py38hdb19cb5_0
  - setuptools=68.0.0=py38h06a4308_0
  - sip=6.6.2=py38h6a678d5_0
  - six=1.16.0=pyhd3eb1b0_1
  - sniffio=1.2.0=py38h06a4308_1
  - soupsieve=2.5=py38h06a4308_0
  - sqlite=3.41.2=h5eee18b_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - sympy=1.5.1=py38_0
  - tbb=2021.8.0=hdb19cb5_0
  - tensorboard=2.14.1=pyhd8ed1ab_0
  - tensorboard-data-server=0.7.0=py38h52d8a92_0
  - terminado=0.17.1=py38h06a4308_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - timm=0.9.7=pyhd8ed1ab_0
  - tinycss2=1.2.1=py38h06a4308_0
  - tk=8.6.12=h1ccaba5_0
  - tokenizers=0.13.3=py38h22610ee_0
  - toml=0.10.2=pyhd8ed1ab_0
  - tomli=2.0.1=py38h06a4308_0
  - torchaudio=2.1.0=py38_cu118
  - torchmetrics=1.2.0=pyhd8ed1ab_0
  - torchtext=0.6.0=py_1
  - torchtriton=2.1.0=py38
  - torchvision=0.16.0=py38_cu118
  - tornado=6.3.3=py38h5eee18b_0
  - tqdm=4.65.0=py38hb070fc8_0
  - traitlets=5.7.1=py38h06a4308_0
  - transformers=4.24.0=py38h06a4308_0
  - typing-extensions=4.7.1=py38h06a4308_0
  - typing_extensions=4.7.1=py38h06a4308_0
  - urllib3=1.26.16=py38h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py38_1
  - websocket-client=0.58.0=py38h06a4308_4
  - werkzeug=2.2.3=py38h06a4308_0
  - wheel=0.41.2=py38h06a4308_0
  - widgetsnbextension=4.0.5=py38h06a4308_0
  - x264=1!157.20191217=h7b6447c_0
  - xz=5.4.2=h5eee18b_0
  - yaml=0.2.5=h7b6447c_0
  - yarl=1.8.1=py38h5eee18b_0
  - zeromq=4.3.4=h2531618_0
  - zipp=3.11.0=py38h06a4308_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.5=hc292b87_0

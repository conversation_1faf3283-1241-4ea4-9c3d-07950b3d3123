---
type: "manual"
---

### 通用化高级学术与技术写作提示词模板

**角色 (Role):**
你是一位在 `多模态情绪识别]` 方面具有深厚造诣的顶尖专家。

**情境 (Context):**
你正在为一份顶级标准的 `[学术论文]` 撰写其核心技术章节。

**核心任务 (Core Task):**
撰写一段关于一个新颖的 `[输入你想要描述的核心概念/模型/方法]` 的技术描述。

**写作准则 (Writing Guidelines):**
*   **专业性 (Professionalism):** 语言必须高度专业、精炼、客观，且信息密度极大。
*   **学术化 (Academic Tone):** 风格必须完全符合你所在领域最高标准的出版物要求。
*   **去 AI 化 (De-AI Tone):** 严格避免 AI 写作中常见的、为了“通俗易懂”而加入的冗余解释、口语化表达、以及程式化的句式结构。文字必须显得自信、权威。

**内容结构蓝图 (Content Blueprint):**

1.  **问题界定 (Problem Definition):**
    从一个当前领域内公认的 `[输入一个现有的理论/模型/工具]` 所面临的特定挑战或内在局限性切入，直接点明痛点。

2.  **方案提出 (Solution Proposal):**
    直接引入你设计的、旨在解决上述问题的 `[输入你的解决方案名称]`。用一句话概括其核心创新点或主要优势。

3.  **机制详述 (Mechanism Description):**
    结构化地阐述 `[你的解决方案名称]` 的内部构造和工作流程。
    *   清晰说明其包含的各个核心 `[组件/步骤/阶段]`。
    *   逐一解释每个 `[组件/步骤]` 的具体功能和作用。
    *   阐明各个部分之间是如何交互、转换或传递信息/物质/状态的。
    *   简要解释为什么选择这样的设计或流程，其理论依据是什么。

4.  **形式化表达 (Formal Representation):**
    使用你所在领域的标准范式，对核心机制进行严谨、无歧义的数学或逻辑表达。请在下方选择一种主要形式：
    *   **选项A (数理/工程类):** 使用 **LaTeX** 格式 ارائه核心数学公式、方程组或算法伪代码。
    *   **选项B (化学/生物类):** 使用标准的 **化学反应式** 或 **生物学通路图** 描述。
    *   **选项C (流程/系统类):** 使用清晰的 **逻辑流程描述** 或 **状态转移** 关系。

5.  **符号与术语定义 (Symbol & Terminology Definition):**
    在形式化表达之后，必须立即跟随一个引导段落（例如以“其中，”或“这里，”开头），对公式、图表或代码中出现的每一个符号、变量、缩写和专业术语进行清晰、完整、无歧义的定义。

**格式与风格禁令 (Style & Formatting Constraints):**
*   **禁用连接词:** 绝对避免使用“首先”、“其次”、“然后”、“接下来”、“最后”、“总之”这类程序性的、破坏学术严肃性的叙事连接词。让文本的逻辑流自然内嵌于专业的句式结构之中。
*   **直接陈述:** 避免使用“我们发现...”、“本研究旨在...”等引导性短语，直接陈述事实和设计。
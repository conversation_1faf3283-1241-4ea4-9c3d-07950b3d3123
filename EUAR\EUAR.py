# 导入日志记录、自定义模块、torch及相关组件
import logging
from modules.transformer import TransformerEncoder
from torch.optim import Adam
import torch
import torch.utils.checkpoint
from torch import nn
from torch.nn import L1Loss, MSELoss
from transformers.models.bert.configuration_bert import BertConfig
import torch.optim as optim
from itertools import chain
from MoE import MoE_block
# from global_configs import TEXT_DIM, ACOUSTIC_DIM, VISUAL_DIM, DEVICE  # 似乎没有用到
from transformers import BertModel

# 获取一个日志记录器实例
logger = logging.getLogger(__name__)


# 定义一个权重初始化函数
def xavier_init(m): 
    """
    使用 Kaiming (He) Normal 初始化线性层权重，这是一种适用于ReLU激活函数的初始化方法。
    偏置项（如果存在）初始化为0。
    """
    if type(m) == nn.Linear:
        nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
        if m.bias is not None:
           m.bias.data.fill_(0.0)

class LinearLayer(nn.Module):
    """
    一个简单的线性层封装，自动应用上面定义的xavier_init初始化。
    """
    def __init__(self, in_dim, out_dim):
        super().__init__()
        self.clf = nn.Sequential(nn.Linear(in_dim, out_dim))
        self.clf.apply(xavier_init)

    def forward(self, x):
        x = self.clf(x)
        return x
    

class EUAR_BertModel(nn.Module):
    """
    自定义的BERT模型，用于处理文本模态。
    它加载预训练的BERT，并在其后接一个1D卷积层进行特征投影。
    """
    def __init__(self, multimodal_config, d_l):
        super().__init__()
        model_path='./prebert' # 指定预训练BERT模型的本地路径
        self.config = BertConfig.from_pretrained(model_path)
        self.encoder=BertModel.from_pretrained(model_path)
  
        self.d_l = d_l
        # 使用1D卷积对BERT的输出进行投影。这可以看作是在序列维度上滑动一个窗口，
        # 捕获局部上下文信息，同时将特征维度从TEXT_DIM(768)调整为d_l。
        self.proj_l = nn.Conv1d(768, self.d_l, kernel_size=3, stride=1, padding=1, bias=False)


    def get_input_embeddings(self):
        return self.embeddings.word_embeddings

    def set_input_embeddings(self, value):
        self.embeddings.word_embeddings = value

    def _prune_heads(self, heads_to_prune):
        """ Transformer的剪枝功能，这里保留了接口但未使用。 """
        for layer, heads in heads_to_prune.items():
            self.encoder.layer[layer].attention.prune_heads(heads)

    def forward(
        self,
        input_ids,
        # visual,
        # acoustic,
        attention_mask=None,
        token_type_ids=None,
        position_ids=None,
        head_mask=None,
        inputs_embeds=None,
        encoder_hidden_states=None,
        encoder_attention_mask=None,
        output_attentions=None,
        output_hidden_states=None,
    ):
        # --- 标准的BERT输入检查和准备 ---
        output_attentions = (
            output_attentions
            if output_attentions is not None
            else self.config.output_attentions
        )
        output_hidden_states = (
            output_hidden_states
            if output_hidden_states is not None
            else self.config.output_hidden_states
        )

        if input_ids is not None and inputs_embeds is not None:
            raise ValueError(
                "You cannot specify both input_ids and inputs_embeds at the same time"
            )
        elif input_ids is not None:
            input_shape = input_ids.size()
        elif inputs_embeds is not None:
            input_shape = inputs_embeds.size()[:-1]
        else:
            raise ValueError(
                "You have to specify either input_ids or inputs_embeds")

        device = input_ids.device if input_ids is not None else inputs_embeds.device

        if attention_mask is None:
            attention_mask = torch.ones(input_shape, device=device)
        if token_type_ids is None:
            token_type_ids = torch.zeros(
                input_shape, dtype=torch.long, device=device)
        if self.config.is_decoder and encoder_hidden_states is not None:
            (
                encoder_batch_size,
                encoder_sequence_length,
                _,
            ) = encoder_hidden_states.size()
            encoder_hidden_shape = (
                encoder_batch_size, encoder_sequence_length)
            if encoder_attention_mask is None:
                encoder_attention_mask = torch.ones(
                    encoder_hidden_shape, device=device)
            encoder_extended_attention_mask = self.invert_attention_mask(
                encoder_attention_mask
            )
        else:
            encoder_extended_attention_mask = None

        # --- 获取BERT输出 ---
        # 得到BERT模型的输出
        embedding_output = self.encoder(input_ids=input_ids,attention_mask=attention_mask)

        # 取最后一层的隐藏状态作为序列输出
        sequence_output = embedding_output.last_hidden_state
        # sequence_output shape: [batch_size, seq_len, 768]
  
        # --- 特征投影 ---
        # 为了使用1D卷积，需要将维度转换为 [batch_size, in_channels, length]
        outputs = sequence_output.transpose(1, 2) # -> [batch_size, 768, seq_len]
        outputs = self.proj_l(outputs) # -> [batch_size, d_l, seq_len]
        
        # 选择序列的最后一个时间步的输出作为整个句子的表示
        # 这是一种池化策略，不同于常见的[CLS] token池化或平均池化
        pooled_output = outputs[:, :, -1] # -> [batch_size, d_l]

        return pooled_output


# 定义一个超参数，但在此文件中似乎未被直接使用
beta   = 1e-3


class EUAR(nn.Module):
    """
    EUAR模型的主体结构。
    它集成了文本、音频和视频三个模态的處理，並通过一个融合模块进行最终的预测。
    """
    def __init__(self, multimodal_config,args,num_labels):
        super().__init__()
        self.num_labels = num_labels

        # ---- 模型超参数定义 ----
        self.d_l = 72          # 文本、音频、视频特征投影后的共同维度
        self.num_heads=4       # Transformer中的注意力头数
        self.dropout = nn.Dropout(0.5) 
        self.attn_dropout = 0.5 

        # ---- 各模态的编码器/处理器 ----
        # 文本模态使用自定义的BERT模型
        self.bert = EUAR_BertModel(multimodal_config, self.d_l)
        
        # 音频和视频模态使用1D卷积进行特征投影，将原始维度映射到d_l
        self.proj_a = nn.Conv1d(74, self.d_l, kernel_size=3, stride=1, padding=1, bias=False)
        self.proj_v = nn.Conv1d(35, self.d_l, kernel_size=3, stride=1, padding=1, bias=False)
        # self.proj_l = nn.Conv1d(768, self.d_l, kernel_size=3, stride=1, padding=1, bias=False) # 已在EUAR_BertModel中实现

        # 为音频和视频分别创建一个Transformer编码器
        self.transa = self.get_network(self_type='l', layers=3)
        self.transv = self.get_network(self_type='l', layers=3)

        # ---- 学习率和优化器 ----
        # 为BERT和模型的其余部分设置不同的学习率（差分学习率）
        self.llr = args.learning_rate1 # BERT部分的学习率
        self.lr = args.learning_rate2  # 其他部分的学习率
        
        # ---- 融合模块 ----
        self.fusion = fusion(self.d_l)

        # ---- 优化器设置 ----
        # 为BERT参数创建一个独立的优化器
        self.optimizer_l = Adam(self.bert.parameters(), lr=self.llr)
        # 为模型其余所有参数创建另一个优化器
        # 使用itertools.chain将不同部分的参数链接起来
        self.optimizer_all = getattr(optim, 'Adam')(chain(self.transa.parameters(), self.transv.parameters(), self.fusion.parameters(), self.proj_a.parameters(), self.proj_v.parameters()), lr=self.lr)

        # ---- 学习率调度器 ----
        # 为两个优化器分别创建学习率调度器，当验证集损失不下降时，自动降低学习率
        self.scheduler_all=optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer_all, "min", patience=2, verbose=True, factor=0.9
        )
        self.scheduler_l=optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer_l, "min", patience=2, verbose=True, factor=0.9
        )

        # 平均池化层，似乎在当前代码中没有被使用
        self.mean = nn.AdaptiveAvgPool1d(1)

    def get_network(self, self_type='l', layers=5):
        """
        一个工厂方法，用于创建TransformerEncoder。
        """
        if self_type in ['l', 'al', 'vl']:
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        else:
            raise ValueError("Unknown network type")

        return TransformerEncoder(embed_dim=embed_dim,
                                  num_heads=self.num_heads,
                                  layers=layers,
                                  attn_dropout=attn_dropout,
                                  relu_dropout=0.3,
                                  res_dropout= 0.3,
                                  embed_dropout=0.2,
                                  attn_mask= False)

    def forward(
        self,
        input_ids,
        visual,
        acoustic,
        label_ids,
        attention_mask=None,
        token_type_ids=None,
        position_ids=None,
        head_mask=None,
        inputs_embeds=None,
        labels=None,
        output_attentions=None,
        output_hidden_states=None,
    ):
        """
        模型的前向传播（训练阶段）。
        """
        # 1. 文本特征提取
        output_l = self.bert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
        )

        # 2. 音频和视频特征提取
        # 准备输入：[batch, seq_len, feature_dim] -> [batch, feature_dim, seq_len]
        acoustic = acoustic.transpose(1, 2)
        visual = visual.transpose(1, 2)

        # 特征维度投影
        acoustic = self.proj_a(acoustic)
        visual = self.proj_v(visual)

        # 准备送入Transformer：[batch, feature_dim, seq_len] -> [seq_len, batch, feature_dim]
        acoustic = acoustic.permute(2, 0, 1)
        visual = visual.permute(2, 0, 1)

        # 通过各自的Transformer编码器
        outputa = self.transa(acoustic)
        outputv = self.transv(visual)
        # 取Transformer最后一层的输出，并取序列的最后一个时间步作为代表
        output_a = outputa[-1]
        output_v = outputv[-1]
        
        # 3. 多模态融合
        # 将三个模态的特征送入融合模块
        # fusion模块返回最终预测和来自MoE的辅助损失
        outputf, loss_u = self.fusion(output_l, output_a, output_v, label_ids)

        # 4. 计算总损失
        # 主任务损失（回归），使用L1Loss (Mean Absolute Error)
        
        """
        outputf.view(-1,) 和 label_ids.view(-1,) 是为了确保两个张量的形状一致，将它们都展平成一维向量。
        然后，loss_fct 会计算这两个向量之间的平均绝对误差，并将结果存入变量 loss_m（m 代表 main，即主任务损失）
        """
        loss_fct = L1Loss()
        loss_m = loss_fct(outputf.view(-1,), label_ids.view(-1,))
        
        # 总损失 = MoE辅助损失 + 主任务损失（加权）
        """loss用于反向传播，更新权重参数"""
        loss = loss_u + loss_m*10

        # 5. 反向传播与优化
        # 使用双优化器进行梯度更新
        self.optimizer_l.zero_grad()#“将梯度清零”
        self.optimizer_all.zero_grad()
        loss.backward(retain_graph = True) # retain_graph=True可能是为了调试或更复杂的梯度流，但在该结构中可能不是必需的
        #  backward() 方法会从当前的张量（这里是 loss）开始，
        # 自动计算出 loss 相对于网络中所有可训练参数（weights）的梯度（gradient）。
        #执行权重更新的步骤
        self.optimizer_all.step()#会找到它所管理的所有参数（除了BERT之外的所有部分），并用这些参数的梯度和它自己的学习率 self.lr 来更新它们。
        self.optimizer_l.step()#会找到它所管理的所有参数（只有BERT的参数），并用这些参数的梯度和它自己的、更小的学习率 self.llr 来更新它们。

        return outputf,self.scheduler_all,self.scheduler_l

    def test(self,
        input_ids,
        visual,
        acoustic,
        attention_mask=None,
        token_type_ids=None,
        position_ids=None,
        head_mask=None,
        inputs_embeds=None,
        labels=None,
        output_attentions=None,
        output_hidden_states=None,):
        """
        模型的测试（推理）阶段。
        流程与forward类似，但不计算损失和进行反向传播。
        """
        output_l = self.bert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,)

        acoustic = acoustic.transpose(1, 2)
        visual = visual.transpose(1, 2)

        acoustic = self.proj_a(acoustic)
        visual = self.proj_v(visual)

        acoustic = acoustic.permute(2, 0, 1)
        visual = visual.permute(2, 0, 1)

        outputa = self.transa(acoustic)
        outputv = self.transv(visual)
        output_a = outputa[-1]
        output_v = outputv[-1]
        # 调用fusion模块的test方法，它只返回最终预测
        outputf = self.fusion.test(output_l, output_a, output_v)

        return outputf


class fusion(nn.Module):
    """
    多模态融合模块。
    首先使用MoE（Mixture of Experts）对每个模态的特征进行处理，
    然后将处理后的特征拼接，并通过一个深度分类器得到最终结果。
    """
    def __init__(self, dim):
        super().__init__()

        self.d_l = dim
        self.dr_rate=0.1
        # 定义一个非常深的多层感知机（MLP）作为最终的分类器/回归器
        # 输入维度是三个模态拼接后的维度 (dim * 3)
        self.classfier=nn.Sequential(LinearLayer(self.d_l*3,self.d_l),
                                     nn.LayerNorm(self.d_l,eps=2e-5),
                                     nn.ReLU(),
                                     LinearLayer(self.d_l,128),
                                     nn.LayerNorm(128,eps=2e-5),
                                     nn.ReLU(),
                                     LinearLayer(128,256),
                                     nn.LayerNorm(256),
                                     nn.ReLU(),
                                     LinearLayer(256,128),
                                     nn.LayerNorm(128,eps=2e-5),
                                     nn.ReLU(),
                                     LinearLayer(128,1))
        
        # 为每个模态创建一个MoE块。
        # num_tokens参数在这里似乎是硬编码的，可能需要与实际输入序列长度匹配或调整。
        self.img_MoE= MoE_block(num_tokens=16, dim=self.d_l, heads=8, dim_head=64)
        self.text_MoE= MoE_block(num_tokens=16, dim=self.d_l, heads=8, dim_head=64)
        self.audio_MoE= MoE_block(num_tokens=16, dim=self.d_l, heads=8, dim_head=64)

    def forward(
        self,
        x_l, # 文本特征
        x_a, # 音频特征
        x_v, # 视频特征
        label_ids
    ):
        # 将各模态特征分别送入对应的MoE块
        text_moe_output,text_moe_loss = self.text_MoE(x_l)
        img_moe_output,img_moe_loss = self.img_MoE(x_v)
        audio_output,audio_moe_loss = self.audio_MoE(x_a)
        
        # 将MoE处理后的特征在特征维度上进行拼接
        feature=torch.cat((audio_output,img_moe_output,text_moe_output),dim=1)
        
        # 将拼接后的特征送入分类器得到最终输出
        output=self.classfier(feature)    
        
        # 计算来自三个MoE块的辅助损失之和（加权）
        loss=audio_moe_loss*1e-5+img_moe_loss*1e-5+text_moe_loss*1e-5
        
        # 返回最终预测和辅助损失
        return output, loss


    def test(
        self,
        x_l,
        x_a,
        x_v
    ):
        """
        融合模块的测试（推理）方法。
        只计算最终输出，忽略MoE的辅助损失。
        """
        # 在推理时，MoE块返回的loss可以被忽略 (用 _ 接收)
        text_moe_output,_=self.text_MoE(x_l)
        img_moe_output,_=self.img_MoE(x_v)
        audio_output,_=self.audio_MoE(x_a) 
        
        feature=torch.cat((audio_output,img_moe_output,text_moe_output),dim=1)
        output=self.classfier(feature)    
        return output

# 导入所需的PyTorch库
import torch
import torch.nn.functional as F
from torch import Tensor, nn


def load_balanced_loss(router_probs, expert_mask):
    """
    计算负载均衡损失（Load Balanced Loss）。
    这个损失函数的目标是鼓励门控网络（router）将输入（tokens）均匀地分配给各个专家。
    它通过惩罚“代理密度”（router_probs的均值）和“实际密度”（expert_mask的均值）之间的不匹配来实现这一点。

    Args:
        router_probs (Tensor): 门控网络为每个token分配给每个专家的概率，形状为 [num_tokens, num_experts]。
        expert_mask (Tensor): 一个二进制掩码，指示哪些专家被选中处理哪些token，形状为 [num_tokens, num_experts]。

    Returns:
        Tensor: 计算出的负载均衡损失。
    """
    num_experts = expert_mask.size(-1)

    # expert_mask.mean(0) 计算每个专家被选中的频率（实际密度）
    density = torch.mean(expert_mask, dim=0)
    # router_probs.mean(0) 计算每个专家的平均路由概率（代理密度）
    density_proxy = torch.mean(router_probs, dim=0)
    
    # 损失是 (代理密度 * 实际密度) 的均值，再乘以一个缩放因子 (num_experts ** 2)
    # 这鼓励了路由概率高的专家也应该有高的选中频率
    loss = torch.mean(density_proxy * density) * (num_experts ** 2)

    return loss

class Gate(nn.Module):
    """
    门控网络（Gating Network），也称为路由器（Router）。
    它的作用是决定每个输入token应该被发送到哪个或哪些专家网络。
    这里实现的是一个Top-K的门控机制。
    """
    def __init__(
        self,
        dim: int,
        num_experts: int,
        capacity_factor: float = 1.0,
        epsilon: float = 1e-6,
        *args,
        **kwargs,
    ):
        """
        初始化门控网络。

        Args:
            dim (int): 输入特征的维度。
            num_experts (int): 专家的数量。
            capacity_factor (float): 容量因子。每个专家的容量定义为 (num_tokens / num_experts) * capacity_factor。
                                     它决定了每个专家最多能处理多少个token。
            epsilon (float): 一个小值，用于防止数值计算中除以零的错误。
        """
        super().__init__()
        self.dim = dim
        self.num_experts = num_experts
        self.capacity_factor = capacity_factor
        self.epsilon = epsilon
        # 一个简单的线性层，将输入特征映射到每个专家的得分
        self.w_gate = nn.Linear(dim, num_experts)

    def forward(self, x: Tensor, use_aux_loss=True):
        """
        门控网络的前向传播。

        Args:
            x (Tensor): 输入张量，形状为 [batch_size, dim]。
            use_aux_loss (bool): 是否计算并返回辅助的负载均衡损失。

        Returns:
            Tuple[Tensor, Tensor or None]:
                - gate_scores (Tensor): 归一化后的门控分数，用于加权专家网络的输出。
                - loss (Tensor or None): 负载均衡损失。
        """
        # 1. 计算门控得分
        # 通过线性层和Softmax函数，得到每个token分配给每个专家的原始概率
        gate_scores = F.softmax(self.w_gate(x), dim=-1)
        loss_gate_scores = gate_scores  # 保存原始概率用于计算loss

        # 2. 确定Top-K的专家
        # 计算每个专家的容量，这里简单地用batch_size乘以容量因子
        capacity = int(self.capacity_factor * x.size(0))
        
        # 找到每个token得分最高的3个专家
        top_k_scores, top_k_indices = gate_scores.topk(3, dim=-1) # 这里 K=3

        # 3. 创建稀疏掩码
        # 创建一个与gate_scores形状相同的零张量，并在top-k的位置置为1
        mask = torch.zeros_like(gate_scores).scatter_(
            1, top_k_indices, 1
        )

        # 4. 应用掩码并归一化
        # 将非top-k的专家的分数清零
        masked_gate_scores = gate_scores * mask

        # 计算每个专家的分数总和，作为归一化的分母
        denominators = (
            masked_gate_scores.sum(0, keepdim=True) + self.epsilon
        )

        # 重新归一化门控分数，使得每个专家的分数总和等于其容量
        gate_scores = (masked_gate_scores / denominators) * capacity

        # 5. 计算辅助损失
        if use_aux_loss:
            # 使用前面定义的函数计算负载均衡损失
            loss = load_balanced_loss(loss_gate_scores, mask)
            return gate_scores, loss

        return gate_scores, None


class FeedForward(nn.Module):
    """
    单个专家网络。
    这个专家的结构比较特殊，类似于一个变分自编码器（VAE）的编码器部分。
    它会为输入计算出一个潜在表示（mu）和对应的不确定性（logvar），并计算KL散度损失。
    """
    def __init__(self,input_dim,output_dim):
        super().__init__()
        # 线性层，用于计算潜在表示的均值 (mu)
        self.mu = nn.Linear(input_dim,output_dim)
        # 线性层，用于计算潜在表示的对数方差 (logvar)
        self.logvar = nn.Linear(input_dim,output_dim)

    def reparameterise(self,mu, logvar):
        """
        重参数化技巧（Reparameterization Trick）。
        允许在保持随机性的同时，让梯度能够反向传播通过采样过程。
        z = mu + epsilon * std
        """
        # 从标准正态分布中采样噪声 epsilon
        std = torch.exp(0.5 * logvar) # 计算标准差
        eps = torch.randn_like(std)
        return mu + std*eps

    def KL_loss(self,mu,logvar):
        """
        计算KL散度损失。
        衡量潜在分布 q(z|x) 与标准正态分布先验 p(z) 之间的差异。
        """
        # KL(q(z|x) || p(z)) 的解析解
        return (-(1+logvar-mu.pow(2)-logvar.exp())/2).sum(dim=1).mean()

    def forward(self,x):
        """
        专家网络的前向传播。
        """
        mu=self.mu(x)
        logvar=self.logvar(x)
        # z = self.reparameterise(mu,logvar) # 注意：这里计算了z但并未使用
        kl_loss = self.KL_loss(mu,logvar)
        # 返回均值（作为专家的输出）、KL损失、以及不确定性（标准差）
        return mu , kl_loss ,torch.exp(logvar)

class MoE(nn.Module):
    """
    混合专家（Mixture of Experts）层。
    该层包含一个门控网络和多个专家网络，并将它们的输出进行加权组合。
    """
    def __init__(
        self,
        dim: int,
        hidden_dim: int,
        output_dim: int,
        num_experts: int,
        capacity_factor: float = 1.0,
        mult: int = 4,
        use_aux_loss: bool = True,
        *args,
        **kwargs,
    ):
        super().__init__()
        self.dim = dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_experts = num_experts
        self.capacity_factor = capacity_factor
        self.mult = mult
        self.use_aux_loss = use_aux_loss

        # 创建一个专家列表，每个专家都是一个FeedForward网络
        self.experts = nn.ModuleList(
            [
                FeedForward(dim, dim)
                for _ in range(num_experts)
            ]
        )

        # 创建门控网络
        self.gate = Gate(
            dim,
            num_experts,
            capacity_factor,
        )

    def forward(self, x: Tensor):
        """
        MoE层的前向传播。
        """
        # 1. 通过门控网络，获取每个专家的权重 (gate_scores) 和负载均衡损失 (loss)
        gate_scores, loss = self.gate(
            x, use_aux_loss=self.use_aux_loss
        )
        
        # 2. 并行计算所有专家的输出
        expert_outputs = []
        loss_kl = []
        Uncertainty =[]
        # 将输入x传递给每个专家，并收集它们的输出、KL损失和不确定性
        for expert_output, kl_loss,sigma in [expert(x) for expert in self.experts]:
            expert_outputs.append(expert_output)
            loss_kl.append(kl_loss)
            Uncertainty.append(sigma)
        
        # 3. 计算总的KL损失
        loss_KL=0
        for i in range(self.num_experts):
            loss_KL+=loss_kl[i]
        
        # 将KL损失的平均值加入到总损失中
        loss = loss +(loss_KL)/self.num_experts
        
        # 4. 计算不确定性损失
        # 将所有专家的不确定性堆叠起来
        Uncertainty = torch.stack(Uncertainty).sum(2).permute(1,0)
        # 不确定性损失 = (不确定性 * 门控分数) 的加权和的均值
        # 这个损失鼓励gate将任务交给不确定性低的专家
        loss_u=(Uncertainty * gate_scores).sum(1).mean()
        loss = loss + loss_u


        # 5. 处理NaN值 (数值稳定性)
        # 检查门控分数中是否有NaN，如果有则替换为0
        if torch.isnan(gate_scores).any():
            print("NaN in gate scores")
            gate_scores[torch.isnan(gate_scores)] = 0

        # 6. 组合专家输出
        # 将所有专家的输出堆叠起来，形状变为 [batch_size, output_dim, num_experts]
        stacked_expert_outputs = torch.stack(
            expert_outputs, dim=-1
        )
        # 同样检查NaN值
        if torch.isnan(stacked_expert_outputs).any():
            stacked_expert_outputs[
                torch.isnan(stacked_expert_outputs)
            ] = 0

        # 7. 加权求和
        # gate_scores: [batch, num_experts] -> [batch, 1, num_experts]
        # stacked_expert_outputs: [batch, dim, num_experts]
        # 逐元素相乘再求和，得到最终输出 [batch, dim]
        moe_output = torch.sum(
            gate_scores.unsqueeze(-2) * stacked_expert_outputs, dim=-1
        )

        return moe_output, loss


class MoE_block(nn.Module):
    """
    一个包含MoE层的模块，可以方便地集成到更大的模型（如Transformer）中。
    它取代了标准Transformer块中的前馈网络（FFN）部分。
    """
    def __init__(
        self,
        dim: int,
        heads: int,
        dim_head: int,
        mult: int = 4,
        dropout: float = 0.1,
        num_experts: int = 4,
        *args,
        **kwargs,
    ):
        super().__init__()
        self.dim = dim
        self.heads = heads
        self.dim_head = dim_head
        self.mult = mult
        self.dropout = dropout

        # 实例化MoE层作为这个块的前馈网络部分
        self.ffn = MoE(
            dim, dim * mult, dim, num_experts, *args, **kwargs
        )
        
        # 层归一化
        self.add_norm = nn.LayerNorm(dim)


    def forward(self, x: Tensor):
        # 注释掉的代码展示了这个MoE块如何在一个标准的Transformer结构中使用
        # resi = x
        # # x, _, _ = self.attn(x) # 注意力层
        # x = x + resi
        # x = self.add_norm(x)
        # add_normed = x
        
        #### MoE 部分 ####
        # 将输入传递给MoE层，得到输出和损失
        x, loss = self.ffn(x)
        
        # x = x + add_normed # 残差连接
        # x = self.add_norm(x) # 层归一化
        return x , loss

# 当该脚本作为主程序运行时，执行以下测试代码
if __name__ == "__main__":
    # 创建一个随机的输入张量
    x = torch.randint(0, 100, (1, 10)).cuda()
    
    # 实例化MoE块模型
    # 注意：这里的参数可能与MoE_block的__init__不完全匹配，这只是一个示例
    model = MoE_block(
        dim=128, heads=8, dim_head=64, num_experts=4
    ).float().cuda()

    # 创建一个嵌入层将输入的整数索引转换为密集向量
    Embedding_layer=nn.Embedding(100,128).cuda()
    x=Embedding_layer(x).squeeze()
    
    # 将输入传递给模型
    out, loss = model(x)

    # 打印输出的形状
    print(out.shape)
    print(loss)
import torch
import torch.nn as nn
import torch.nn.functional as F
from trains.subNets import BertTextEncoder
from trains.subNets.transformers_encoder.transformer import TransformerEncoder
from trains.singleTask.model.router import router
from trains.utils import MetricsTop, dict_to_str, eva_imp, uni_distill, entropy_balance

# ----------------- 步骤 1: 导入 SIGMA 核心模块 -----------------
from mamba_modal.DEMO1.gated_mamba import SIGMALayers


# ----------------- 交互模块占位符 (Placeholder for Interaction Module) -----------------
# 在实际应用中，这里应替换为包含 `cross_selective_scan` 的真实跨模态 Mamba 模块。
# 为保证代码可运行，此处使用一个简化的线性交互层作为占位符。
class CrossMambaBlock(nn.Module):
    def __init__(self, d_model):
        super().__init__()
        self.d_model = d_model
        # 简化交互：将两个模态拼接后通过线性层，模拟信息融合
        self.interaction_proj1 = nn.Linear(d_model * 2, d_model)
        self.interaction_proj2 = nn.Linear(d_model * 2, d_model)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

    def forward(self, x1, x2):
        # 输入维度: (batch, seq_len, dim)
        # 输出维度: (batch, seq_len, dim)

        # 将 x2 的信息融入 x1
        fused_for_x1 = torch.cat([x1, x2], dim=-1)
        interacted_x1 = self.interaction_proj1(fused_for_x1)
        out1 = self.norm1(x1 + F.relu(interacted_x1))

        # 将 x1 的信息融入 x2
        fused_for_x2 = torch.cat([x1, x2], dim=-1) # 使用相同的拼接顺序以简化
        interacted_x2 = self.interaction_proj2(fused_for_x2)
        out2 = self.norm2(x2 + F.relu(interacted_x2))

        return out1, out2


# ----------------- 对抗蒸馏的判别器模块 -----------------
class ModalityDiscriminator(nn.Module):
    """
    一个简单的判别器网络，用于对抗式蒸馏。
    它的任务是接收一个特征向量，并判断该特征属于哪个模态（文本、视频、音频）。
    """
    def __init__(self, d_model, n_hidden=64):
        super().__init__()
        self.d_model = d_model
        self.network = nn.Sequential(
            nn.Linear(d_model, n_hidden),
            nn.ReLU(inplace=True),
            nn.Dropout(0.4),
            nn.Linear(n_hidden, 3) # 输出3个类别的 logits
        )

    def forward(self, x):
        return self.network(x)


class DEMO(nn.Module):
    """
    EMOE 模型的核心实现。
    它整合了三个模态（文本、音频、视频）的输入，并通过一个动态的、基于专家混合（MoE）的机制来融合它们。
    该模型包含两个关键部分：
    1. Mixture of Modality Experts (MoME): 为每个样本动态计算模态权重。
    2. Unimodal Distillation (UD): 利用单模态的预测能力来指导多模态的学习。
    """
    def __init__(self, args):
        """
        模型初始化。
        参数:
            args (Namespace): 包含所有模型配置和超参数的命名空间。
        """
        super(DEMO, self).__init__()
        # 1. 初始化文本编码器 (可选，使用BERT)
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert

        # 2. 从args中解包和设置模型参数
        dst_feature_dims, nheads = args.dst_feature_dim_nheads # 目标特征维度和注意力头数
        
        # 根据数据集名称和是否对齐，设置三个模态的序列长度
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else: # 未对齐的数据有不同的序列长度
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500
        
        self.aligned = args.need_data_aligned # 是否对齐的标志
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims # 原始特征维度
        self.d_l = self.d_a = self.d_v = dst_feature_dims # 变换后统一的特征维度
        self.num_heads = nheads # Transformer中的多头注意力头数
        self.layers = args.nlevels # Transformer的层数
        self.attn_dropout = args.attn_dropout # 通用注意力dropout
        self.attn_dropout_a = args.attn_dropout_a # 音频专用注意力dropout
        self.attn_dropout_v = args.attn_dropout_v # 视频专用注意力dropout
        self.relu_dropout = args.relu_dropout # ReLU激活后的dropout
        self.embed_dropout = args.embed_dropout # 输入嵌入的dropout
        self.res_dropout = args.res_dropout # 残差连接的dropout
        self.output_dropout = args.output_dropout # 输出层的dropout
        self.text_dropout = args.text_dropout # 文本特征的dropout
        self.attn_mask = args.attn_mask # 是否使用注意力掩码
        self.fusion_method = args.fusion_method # 融合方法 ('sum' 或 'concat')
        output_dim = 1 # 回归任务的输出维度为1
        self.args = args

        # 3. 定义模态投影层 (使用1D卷积将不同维度的输入投影到统一维度)
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)

        # 4. 定义编码器层 (在进入Transformer之前对特征进行初步编码)
        # 注意：这里所有模态共享同一个encoder_c，用于提取模态不变特征 (common features)
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        # 这几个编码器在当前实现中并未使用，可能为未来扩展保留
        self.encoder_l = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        self.encoder_v = nn.Conv1d(self.d_v, self.d_v, kernel_size=1, padding=0, bias=False)
        self.encoder_a = nn.Conv1d(self.d_a, self.d_a, kernel_size=1, padding=0, bias=False)

        # 5. 为每个模态实例化独立的Transformer编码器
        # self.self_attentions_l = self.get_network(self_type='l')
        # self.self_attentions_v = self.get_network(self_type='v')
        # self.self_attentions_a = self.get_network(self_type='a')

        # ----------------- 步骤 2: 实例化新的 SIGMA 模块 -----------------
        # 使用新的工厂方法为每个模态创建 SIGMA 序列处理器
        self.sigma_processor_l = self.get_sigma_network(self_type='l')
        self.sigma_processor_v = self.get_sigma_network(self_type='v')
        self.sigma_processor_a = self.get_sigma_network(self_type='a')

        # ----------------- 步骤 2.5: 实例化两两交互的交叉 Mamba 模块 -----------------
        self.interaction_lv = CrossMambaBlock(d_model=self.d_l)
        self.interaction_la = CrossMambaBlock(d_model=self.d_l)
        self.interaction_va = CrossMambaBlock(d_model=self.d_l)

        # ----------------- 步骤 2.6: 实例化模态判别器 (用于对抗训练) -----------------
        self.discriminator = ModalityDiscriminator(d_model=self.d_l)

        # 6. 为每个单模态定义独立的预测头 (用于Unimodal Distillation)
        self.proj1_l = nn.Linear(self.d_l, self.d_l)
        self.proj2_l = nn.Linear(self.d_l, self.d_l)
        self.out_layer_l = nn.Linear(self.d_l, output_dim)
        self.proj1_v = nn.Linear(self.d_l, self.d_l)
        self.proj2_v = nn.Linear(self.d_l, self.d_l)
        self.out_layer_v = nn.Linear(self.d_l, output_dim)
        self.proj1_a = nn.Linear(self.d_l, self.d_l)
        self.proj2_a = nn.Linear(self.d_l, self.d_l)
        self.out_layer_a = nn.Linear(self.d_l, output_dim)

        # 为交互后的模态定义新的、独立的预测头
        self.proj1_l_interacted = nn.Linear(self.d_l, self.d_l)
        self.proj2_l_interacted = nn.Linear(self.d_l, self.d_l)
        self.out_layer_l_interacted = nn.Linear(self.d_l, output_dim)

        self.proj1_v_interacted = nn.Linear(self.d_l, self.d_l)
        self.proj2_v_interacted = nn.Linear(self.d_l, self.d_l)
        self.out_layer_v_interacted = nn.Linear(self.d_l, output_dim)

        self.proj1_a_interacted = nn.Linear(self.d_l, self.d_l)
        self.proj2_a_interacted = nn.Linear(self.d_l, self.d_l)
        self.out_layer_a_interacted = nn.Linear(self.d_l, output_dim)

        # 7. 为融合后的多模态特征定义预测头
        if self.fusion_method == "sum": # 加权求和融合
            self.proj1_c = nn.Linear(self.d_l, self.d_l)
            self.proj2_c = nn.Linear(self.d_l, self.d_l)
            self.out_layer_c = nn.Linear(self.d_l, output_dim)
        elif self.fusion_method == "concat": # 加权拼接融合
            self.proj1_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.proj2_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.out_layer_c = nn.Linear(self.d_l*3, output_dim)

        # 8. 实例化路由网络 (Router Network)，论文MoME部分的核心
        # 输入维度是三个模态特征拼接后的总维度，输出维度是3（对应三个模态的权重）
        self.Router = router(self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l, 3, self.args.temperature)
        
        # 9. 定义线性层，用于在数据未对齐时，将音视频的序列长度变换到与文本相同
        self.transfer_a_ali = nn.Linear(self.len_a, self.len_l)
        self.transfer_v_ali = nn.Linear(self.len_v, self.len_l)


    # ----------------- 步骤 3: 添加新的 SIGMA 模块工厂函数 -----------------
    def get_sigma_network(self, self_type='l', layers=-1):
        """
        工厂方法，用于创建和配置一个 SIGMALayers 模块。
        这个模块将替代原有的 TransformerEncoder 来处理序列数据。
        """
        # 使用在 args 中定义的统一特征维度
        embed_dim = self.d_l
        
        # 从 args 中获取 Mamba 特定的超参数，如果未定义则使用默认值
        # 这使得我们可以在运行脚本时灵活调整这些参数
        d_state = getattr(self.args, 'd_state', 16)
        d_conv = getattr(self.args, 'd_conv', 4)
        expand = getattr(self.args, 'expand', 2)

        # 实例化并返回 SIGMALayers
        return SIGMALayers(
            d_model=embed_dim,
            d_state=d_state,
            d_conv=d_conv,
            expand=expand,
            dropout=self.embed_dropout,  # 复用 DEMO 模型中已有的 dropout 参数
            num_layers=max(self.layers, layers)
        )


    def get_network(self, self_type='l', layers=-1):
        """
        一个工厂/辅助方法，用于根据指定的模态类型来创建和配置一个完整的Transformer编码器网络。
        这种设计模式使得为不同模态（文本、音频、视频）创建具有细微差别（如不同的dropout率）
        的专属编码器变得非常方便，同时又最大化地复用了代码。

        参数:
            self_type (str): 指定要创建的编码器所属的模态类型。
                             可选值为 'l' (语言), 'a' (音频), 'v' (视频)。
            layers (int): 可选参数，用于覆盖在args中设置的默认Transformer层数。
                          如果为-1，则使用默认层数。

        返回:
            TransformerEncoder: 一个配置完成的、准备好处理序列数据的Transformer编码器实例。
        """
        # 1. 根据模态类型，选择对应的特征维度和注意力dropout率。
        #    这允许我们为不同模态（例如，音频可能需要比文本更高的dropout率）进行精细化的超参数调整。
        if self_type == 'l':
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        elif self_type == 'a':
            embed_dim, attn_dropout = self.d_a, self.attn_dropout_a
        elif self_type == 'v':
            embed_dim, attn_dropout = self.d_v, self.attn_dropout_v
        else:
            raise ValueError("未知的网络类型")

        # 2. 实例化并返回一个TransformerEncoder。
        #    这里传入了所有从args中读取的、与Transformer相关的超参数。
        return TransformerEncoder(embed_dim=embed_dim,             # 特征维度，例如 128
                                  num_heads=self.num_heads,         # 多头注意力机制中的"头"数，例如 8
                                  layers=max(self.layers, layers),  # 编码器层数，例如 4
                                  attn_dropout=attn_dropout,        # 注意力权重矩阵的dropout率
                                  relu_dropout=self.relu_dropout,   # 全连接层中ReLU激活后的dropout率
                                  res_dropout=self.res_dropout,     # 残差连接上的dropout率
                                  embed_dropout=self.embed_dropout,# 输入嵌入（embedding）的dropout率
                                  attn_mask=self.attn_mask)         # 是否在自注意力计算中使用掩码

    def get_net(self, name):
        """一个简单的辅助函数，通过名字获取模型的一个层。"""
        return getattr(self, name)

    def forward(self, text, audio, video):
        """
        模型的前向传播逻辑。
        """
        # 1. 文本特征预处理 (如果使用BERT)
        if self.use_bert:
            text = self.text_model(text)
        
        # 将各模态输入转置为 (batch_size, feature_dim, seq_len) 以适应Conv1d
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training)
        x_a = audio.transpose(1, 2)
        x_v = video.transpose(1, 2)

        # 2. 准备送入路由网络(Router)的输入
        # 如果数据未对齐，先通过线性层统一序列长度
        if not self.aligned:
            # permute 用于交换维度以匹配nn.Linear的输入要求
            audio_ = self.transfer_a_ali(audio.permute(0, 2, 1)).permute(0, 2, 1)
            video_ = self.transfer_v_ali(video.permute(0, 2, 1)).permute(0, 2, 1)
            # 拼接三个模态的原始特征
            m_i = torch.cat((text, video_, audio_), dim=2)
        else:
            m_i = torch.cat((text, video, audio), dim=2)
        
        # 3. 调用路由网络，计算模态权重 m_w
        m_w = self.Router(m_i)

        # 4. 特征投影：使用1D卷积将各模态输入投影到统一的特征维度
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)

        # 5. 共享编码器：所有模态通过一个共享的1D卷积层进行初步编码
        c_l = self.encoder_c(proj_x_l)
        c_v = self.encoder_c(proj_x_v)
        c_a = self.encoder_c(proj_x_a)

        # c_l, c_v, c_a 的维度是 [序列长度, 批次大小, 特征维度]
        # 这是为每个模态准备好的、进入各自专属Transformer前的数据
        c_l = c_l.permute(2, 0, 1)
        c_v = c_v.permute(2, 0, 1)
        c_a = c_a.permute(2, 0, 1)

        # 6. 模态专属Transformer编码与池化
        #    以下三个代码块的逻辑完全相同，分别为每个模态执行：
        #    a. 通过各自的Transformer编码器进行深度特征提取。
        #    b. 对Transformer的输出进行池化，将序列信息压缩成一个固定大小的向量。

        # ----------------- 步骤 4: 使用 SIGMA 模块替换原有的 Transformer 处理逻辑 -----------------
        
        # --- 语言模态处理 (使用SIGMA模块) ---
        # a. 维度转置以匹配SIGMA模块的输入要求: (batch, seq_len, dim)
        c_l_permuted = c_l.permute(1, 0, 2)
        # b. 将序列送入SIGMA处理器
        sigma_output_l = self.sigma_processor_l(c_l_permuted)#调用 SIGMA 模块
        # c. 池化操作：取序列的最后一个时间步的输出作为最终表示
        c_l_att_pure = sigma_output_l[:, -1, :]

        # --- 视频模态处理 (使用SIGMA模块) ---
        c_v_permuted = c_v.permute(1, 0, 2)
        sigma_output_v = self.sigma_processor_v(c_v_permuted)#调用 SIGMA 模块
        c_v_att_pure = sigma_output_v[:, -1, :]
        
        # --- 音频模态处理 (使用SIGMA模块) ---
        c_a_permuted = c_a.permute(1, 0, 2)
        sigma_output_a = self.sigma_processor_a(c_a_permuted)
        c_a_att_pure = sigma_output_a[:, -1, :]

        # 7. 计算单模态的预测输出 (logits)，用于 Unimodal Distillation
        #    注意：这里使用的是交互前的纯粹特征 c_x_att_pure
        l_proj = self.proj2_l(
            F.dropout(F.relu(self.proj1_l(c_l_att_pure), inplace=True), p=self.output_dropout,
                      training=self.training))
        l_proj += c_l_att_pure # 残差连接
        logits_l = self.out_layer_l(l_proj)
        v_proj = self.proj2_v(
            F.dropout(F.relu(self.proj1_v(c_v_att_pure), inplace=True), p=self.output_dropout,
                      training=self.training))
        v_proj += c_v_att_pure
        logits_v = self.out_layer_v(v_proj)
        a_proj = self.proj2_a(
            F.dropout(F.relu(self.proj1_a(c_a_att_pure), inplace=True), p=self.output_dropout,
                      training=self.training))
        a_proj += c_a_att_pure
        logits_a = self.out_layer_a(a_proj)

        # ======================== 新增的并行交互路径 (开始) ========================
        #
        # 核心思想：
        # 路径一（已完成）: 使用池化后的单模态特征 (c_l_att) 直接进行单模态预测。
        # 路径二（当前部分）: 使用未经池化的完整序列 (c_l_permuted 等) 进行深度交互，
        #                     生成更强大的特征，专门用于最终的多模态融合。

        # 1. 执行成对交互 (输入是未经池化的完整序列)
        #    注意: CrossMambaBlock期望输入为 (batch, seq_len, dim)
        #    文本 <-> 视频 交互
        l_after_v, v_after_l = self.interaction_lv(sigma_output_l, sigma_output_v)
        #    文本 <-> 音频 交互
        l_after_a, a_after_l = self.interaction_la(sigma_output_l, sigma_output_a)
        #    视频 <-> 音频 交互
        v_after_a, a_after_v = self.interaction_va(sigma_output_v, sigma_output_a)

        # 2. 聚合交互后的信息 (使用残差连接)
        final_l_sequence = sigma_output_l + l_after_v + l_after_a
        final_v_sequence = sigma_output_v + v_after_l + v_after_a
        final_a_sequence = sigma_output_a + a_after_l + a_after_v

        # 3. 对交互和聚合后的序列进行池化，得到用于后续步骤的专属特征
        c_l_att_interacted = final_l_sequence[:, -1, :]
        c_v_att_interacted = final_v_sequence[:, -1, :]
        c_a_att_interacted = final_a_sequence[:, -1, :]
        # ======================== 新增的并行交互路径 (结束) ========================

        # 计算交互后各单模态的预测输出 (logits for interacted modalities)
        #    注意：这里使用的是交互后的特征 c_x_att_interacted
        l_proj_interacted = self.proj2_l_interacted(
            F.dropout(F.relu(self.proj1_l_interacted(c_l_att_interacted), inplace=True), p=self.output_dropout,
                      training=self.training))
        l_proj_interacted += c_l_att_interacted  # 残差连接
        logits_l_interacted = self.out_layer_l_interacted(l_proj_interacted)

        v_proj_interacted = self.proj2_v_interacted(
            F.dropout(F.relu(self.proj1_v_interacted(c_v_att_interacted), inplace=True), p=self.output_dropout,
                      training=self.training))
        v_proj_interacted += c_v_att_interacted
        logits_v_interacted = self.out_layer_v_interacted(v_proj_interacted)

        a_proj_interacted = self.proj2_a_interacted(
            F.dropout(F.relu(self.proj1_a_interacted(c_a_att_interacted), inplace=True), p=self.output_dropout,
                      training=self.training))
        a_proj_interacted += c_a_att_interacted
        logits_a_interacted = self.out_layer_a_interacted(a_proj_interacted)

        #print("c_l_att.shape, c_v_att.shape, c_a_att.shape", c_l_att.shape, c_v_att.shape, c_a_att.shape)
        
        # 8. 动态特征融合 (Dynamic Fusion)
        #    关键改动：使用交互后的特征 `..._interacted` 进行融合
        if self.fusion_method == "sum": 
            # 遍历batch中的每个样本，使用其专属的权重 m_w[i] 进行加权求和
            for i in range(m_w.shape[0]):
                c_f = c_l_att_interacted[i] * m_w[i][0] + c_v_att_interacted[i] * m_w[i][1] + c_a_att_interacted[i] * m_w[i][2]
                if i == 0: c_fusion = c_f.unsqueeze(0)
                else: c_fusion = torch.cat([c_fusion, c_f.unsqueeze(0)], dim=0)   
        elif self.fusion_method == "concat":        
            # 遍历batch中的每个样本，进行加权拼接
            for i in range(m_w.shape[0]):
                c_f = torch.cat([c_l_att_interacted[i] * m_w[i][0], c_v_att_interacted[i] * m_w[i][1], c_a_att_interacted[i] * m_w[i][2]], dim=0) * 3
                if i == 0: c_fusion = c_f.unsqueeze(0)
                else: c_fusion = torch.cat([c_fusion, c_f.unsqueeze(0)], dim=0)   


        # 9. 计算多模态融合后的预测输出
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(c_fusion), inplace=True), p=self.output_dropout,
                      training=self.training))
        c_proj += c_fusion # 残差连接
        logits_c = self.out_layer_c(c_proj)


        # 10. 将所有需要用于计算损失的中间结果打包返回
        res = {
            'logits_c': logits_c,           # 多模态预测结果
            'logits_l': logits_l,           # 文本单模态预测结果
            'logits_v': logits_v,           # 视频单模态预测结果
            'logits_a': logits_a,           # 音频单模态预测结果
            'logits_l_interacted': logits_l_interacted,
            'logits_v_interacted': logits_v_interacted,
            'logits_a_interacted': logits_a_interacted,
            'channel_weight': m_w,          # 路由网络输出的模态权重
            'c_proj': c_proj,               # 多模态最终特征
            'l_proj': l_proj,               # 文本单模态最终特征
            'v_proj': v_proj,               # 视频单模态最终特征
            'a_proj': a_proj,               # 音频单模态最终特征
            'l_proj_interacted': l_proj_interacted, # 交互后文本特征
            'v_proj_interacted': v_proj_interacted, # 交互后视频特征
            'a_proj_interacted': a_proj_interacted, # 交互后音频特征
            'c_fea': c_fusion,              # 融合后的特征(进入最后proj层之前)
        }
        return res
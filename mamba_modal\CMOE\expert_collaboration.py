import torch
import torch.nn as nn
import torch.nn.functional as F
from mamba_modal.mamba_block import MambaBlock

class ModalExpert(nn.Module):
    """
    单一模态专家分支。
    对单一模态的高层特征进行进一步建模和抽象，
    为后续的多模态融合或专家合作机制提供判别性更强的特征或预测结果。
    """
    def __init__(self, input_dim, hidden_dim, output_dim, use_mamba=False, nlevels=4):
        super(ModalExpert, self).__init__()
        self.use_mamba = use_mamba
        
        if use_mamba:
            # 使用Mamba作为专家结构（适用于序列数据）
            self.expert = MambaBlock(input_dim, nlevels)
            # Mamba输出后的投影层
            self.proj = nn.Linear(input_dim, output_dim)
        else:
            # 使用MLP作为专家结构
            self.expert = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_dim, output_dim)
            )
    
    def forward(self, x):
        """
        前向传播，输入为该模态的特征，输出为预测或高层特征。
        :param x: Tensor, shape=[batch_size, seq_len, input_dim]或[batch_size, input_dim]
        :return: Tensor, shape=[batch_size, output_dim]
        """
        if self.use_mamba:
            # Mamba处理序列数据
            x = self.expert(x)
            # 取最后一个时间步作为输出
            x = x[:, -1, :]
            # 投影到指定维度
            x = self.proj(x)
        else:
            # MLP处理非序列数据
            x = self.expert(x)
        return x

class ExpertCollaboration(nn.Module):
    """
    专家合作机制，基于B项目的CoE思想，实现多模态专家的协同决策。
    每个专家处理一种模态，所有专家输出通过可学习权重进行融合。
    """
    def __init__(self, args):
        super(ExpertCollaboration, self).__init__()
        # 特征提取部分（保留A的特征提取方式）
        self.proj_l = nn.Linear(args.orig_d_l, args.d_l)
        self.proj_a = nn.Linear(args.orig_d_a, args.d_a)
        self.proj_v = nn.Linear(args.orig_d_v, args.d_v)
        
        # 特征编码部分（可选择是否使用Mamba）
        self.use_mamba_encoder = getattr(args, 'use_mamba_encoder', True)
        if self.use_mamba_encoder:
            self.encoder_l = MambaBlock(args.d_l, args.nlevels)
            self.encoder_a = MambaBlock(args.d_a, args.nlevels)
            self.encoder_v = MambaBlock(args.d_v, args.nlevels)
        
        # 模态专家部分（每个模态一个专家）
        expert_hidden_dim = getattr(args, 'expert_hidden_dim', 128)
        expert_output_dim = getattr(args, 'expert_output_dim', 64)
        use_mamba_expert = getattr(args, 'use_mamba_expert', False)
        
        self.expert_l = ModalExpert(args.d_l, expert_hidden_dim, expert_output_dim, use_mamba_expert, args.nlevels)
        self.expert_a = ModalExpert(args.d_a, expert_hidden_dim, expert_output_dim, use_mamba_expert, args.nlevels)
        self.expert_v = ModalExpert(args.d_v, expert_hidden_dim, expert_output_dim, use_mamba_expert, args.nlevels)
        
        # 专家数量
        self.num_experts = 3  # 文本、音频、视觉
        
        # 可学习的专家权重（置信度张量）
        # 全局可学习权重，形状为[num_classes, num_experts]
        self.num_classes = getattr(args, 'num_classes', 1)  # 默认为回归任务
        if getattr(args, 'class_specific_weights', False):
            # 类别相关的权重
            self.Theta = nn.Parameter(torch.ones(self.num_classes, self.num_experts))
        else:
            # 全局权重
            self.Theta = nn.Parameter(torch.ones(self.num_experts))
        
        # 融合后的输出头
        fusion_dim = expert_output_dim
        self.fusion_dropout = nn.Dropout(getattr(args, 'fusion_dropout', 0.2))
        self.output_head = nn.Linear(fusion_dim, args.output_dim)
        
        # 是否使用样本相关的动态权重
        self.use_dynamic_weights = getattr(args, 'use_dynamic_weights', False)
        if self.use_dynamic_weights:
            # 样本相关的权重生成器
            weight_input_dim = expert_output_dim * self.num_experts
            self.weight_generator = nn.Sequential(
                nn.Linear(weight_input_dim, 64),
                nn.ReLU(),
                nn.Linear(64, self.num_experts),
                nn.Softmax(dim=-1)
            )
    
    def forward(self, text, audio, video):
        """
        前向传播
        :param text: [batch_size, seq_len, orig_d_l]
        :param audio: [batch_size, seq_len, orig_d_a]
        :param video: [batch_size, seq_len, orig_d_v]
        :return: dict，包含模型输出和中间结果
        """
        batch_size = text.size(0)
        
        # 1. 特征投影
        text_proj = self.proj_l(text)
        audio_proj = self.proj_a(audio)
        video_proj = self.proj_v(video)
        
        # 2. 特征编码（如果使用Mamba）
        if self.use_mamba_encoder:
            text_enc = self.encoder_l(text_proj)
            audio_enc = self.encoder_a(audio_proj)
            video_enc = self.encoder_v(video_proj)
        else:
            text_enc = text_proj
            audio_enc = audio_proj
            video_enc = video_proj
        
        # 3. 模态专家处理
        text_out = self.expert_l(text_enc)
        audio_out = self.expert_a(audio_enc)
        video_out = self.expert_v(video_enc)
        
        # 收集所有专家输出
        expert_outputs = [text_out, audio_out, video_out]
        
        # 4. 专家合作机制（权重融合）
        if self.use_dynamic_weights:
            # 样本相关的动态权重
            concat_features = torch.cat(expert_outputs, dim=1)
            weights = self.weight_generator(concat_features)  # [batch_size, num_experts]
            # 加权融合
            fusion = torch.zeros_like(text_out)
            for i in range(self.num_experts):
                fusion += weights[:, i:i+1] * expert_outputs[i]
        else:
            # 使用可学习的全局权重
            if self.Theta.dim() == 2:  # 类别相关权重
                # 先用平均权重进行融合，得到初步预测
                avg_fusion = sum(expert_outputs) / self.num_experts
                avg_logits = self.output_head(avg_fusion)
                pred_class = torch.argmax(avg_logits, dim=1)  # [batch_size]
                
                # 根据预测类别选择对应的权重
                weights = torch.zeros(batch_size, self.num_experts, device=text.device)
                for i in range(batch_size):
                    weights[i] = F.softmax(self.Theta[pred_class[i]], dim=0)
                
                # 加权融合
                fusion = torch.zeros_like(text_out)
                for i in range(self.num_experts):
                    fusion += weights[:, i:i+1] * expert_outputs[i]
            else:  # 全局权重
                weights = F.softmax(self.Theta, dim=0)  # [num_experts]
                # 加权融合
                fusion = sum(weights[i] * expert_outputs[i] for i in range(self.num_experts))
        
        # 5. 融合后处理
        fusion = self.fusion_dropout(fusion)
        logits = self.output_head(fusion)
        
        # 返回结果
        return {
            'logits': logits,  # 最终预测结果
            'expert_outputs': expert_outputs,  # 各专家输出
            'expert_weights': weights,  # 专家权重
            'text_feat': text_out,  # 文本专家输出
            'audio_feat': audio_out,  # 音频专家输出
            'video_feat': video_out,  # 视频专家输出
            'fusion_feat': fusion  # 融合特征
        }

class ExpertCollaborationWithBranches(ExpertCollaboration):
    """
    带有分支输出的专家合作模型，兼容原EMOE的输出格式。
    除了最终融合输出外，还保留各模态分支的独立输出，便于计算分支损失和蒸馏。
    """
    def __init__(self, args):
        super(ExpertCollaborationWithBranches, self).__init__(args)
        
        # 额外添加各模态分支的输出头
        expert_output_dim = getattr(args, 'expert_output_dim', 64)
        self.branch_l = nn.Linear(expert_output_dim, args.output_dim)
        self.branch_a = nn.Linear(expert_output_dim, args.output_dim)
        self.branch_v = nn.Linear(expert_output_dim, args.output_dim)
    
    def forward(self, text, audio, video):
        # 获取基本输出
        base_output = super().forward(text, audio, video)
        
        # 添加分支输出
        logits_l = self.branch_l(base_output['text_feat'])
        logits_a = self.branch_a(base_output['audio_feat'])
        logits_v = self.branch_v(base_output['video_feat'])
        
        # 扩展输出字典
        output = {
            'logits_c': base_output['logits'],  # 兼容EMOE的输出命名
            'logits_l': logits_l,
            'logits_a': logits_a,
            'logits_v': logits_v,
            'channel_weight': base_output['expert_weights'],
            'l_proj': base_output['text_feat'],
            'a_proj': base_output['audio_feat'],
            'v_proj': base_output['video_feat'],
            'c_proj': base_output['fusion_feat']
        }
        
        return output 
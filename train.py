import torch
from run import EMOE_run
from mamba_modal.DEMO2.demo_run import DEMO_run

# CUDNN优化配置
torch.backends.cudnn.enabled = True
torch.backends.cudnn.benchmark = True  # 自动选择最优卷积算法
torch.backends.cudnn.allow_tf32 = True  # 启用TF32加速卷积

# 如果有Ampere架构GPU，也可以启用矩阵乘法的TF32
torch.backends.cuda.matmul.allow_tf32 = True

if __name__ == '__main__':
    DEMO_run(model_name='demo', dataset_name='mosei', is_tune=False, seeds=[1111], model_save_dir="./pt", res_save_dir="./result", log_dir="./log", mode='train')
